{"version": 3, "sources": ["out-editor/nls.messages.ru.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\nglobalThis._VSCODE_NLS_MESSAGES=[\"{0} ({1})\",\"входные данные\",\"С учетом регистра\",\"Слово целиком\",\"Использовать регулярное выражение\",\"входные данные\",\"Сохра<PERSON>ить регистр\",\"Проверьте этот аспект в доступном представлении с помощью {0}.\",\"Проверьте этот аспект в представлении с поддержкой специальных возможностей с помощью команды \\\"Открыть представление с поддержкой специальных возможностей\\\", которую в настоящее время нельзя активировать с помощью настраиваемого сочетания клавиш.\",\"Ошибка: {0}\",\"Предупреждение: {0}\",\"Информация: {0}\",\" или {0} для журнала\",\" ({0} для журнала)\",\"Очищенные входные данные\",\"свободный\",\"Поле выбора\",\"Дополнительные действия...\",\"Фильтр\",\"Нечеткое совпадение\",\"Введите текст для фильтра\",\"Ввод для поиска\",\"Ввод для поиска\",\"Закрыть\",\"Нет результатов\",\"Результаты не найдены.\",null,\"(пусто)\",\"{0}: {1}\",\"Произошла системная ошибка ({0})\",\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\"{0} (всего ошибок: {1})\",\"Произошла неизвестная ошибка. Подробные сведения см. в журнале.\",\"CTRL\",\"SHIFT\",\"ALT\",\"Windows\",\"CTRL\",\"SHIFT\",\"ALT\",\"Super\",\"CTRL\",\"SHIFT\",\"Параметр\",\"Команда\",\"CTRL\",\"SHIFT\",\"ALT\",\"Windows\",\"CTRL\",\"SHIFT\",\"ALT\",\"Super\",null,null,null,null,null,\"Размещать на конце даже для более длинных строк\",\"Размещать на конце даже для более длинных строк\",\"Дополнительные курсоры удалены.\",\"&&Отменить\",\"Отменить\",\"&&Повторить\",\"Вернуть\",\"&&Выделить все\",\"Выбрать все\",\"Удерживайте клавишу {0}чтобы навести указатель мыши\",\"Идет загрузка...\",\"Число курсоров ограничено {0}. Для проведения крупных изменений рекомендуется использовать [поиск и замену](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) или увеличить значение параметра ограничения нескольких курсоров в редакторе.\",\"Увеличить значение ограничения нескольких курсоров\",\"Переключатель свертывания неизмененных регионов\",\"Показать или скрыть перемещенные блоки кода\",\"Переключатель \\\"Использовать встроенное представление при ограниченном пространстве\\\"\",\"Редактор несовпадений\",\"Переключить сторону\",\"Выйти из перемещения сравнения\",\"Свернуть все неизмененные области\",\"Показать все неизмененные области\",\"Отменить изменения\",\"Доступное представление различий\",\"Перейти к следующему различию\",\"Перейти к предыдущему различию\",\"Значок \\\"Вставить\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\"Значок \\\"Удалить\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\"Значок \\\"Закрыть\\\" в средстве просмотра с поддержкой специальных возможностей инструмента сравнений\",\"Закрыть\",\"Доступное средство просмотра инструмента сравнения. Используйте клавиши СТРЕЛКА ВВЕРХ и СТРЕЛКА ВНИЗ для перемещения.\",\"нет измененных строк\",\"1 строка изменена\",\"Строк изменено: {0}\",\"Различие {0} из {1}: исходная строка {2}, {3}, измененная строка {4}, {5}\",\"пустой\",\"{0} неизмененная строка {1}\",\"{0} исходная строка {1} измененная строка {2}\",\"+ {0} измененная строка {1}\",\"- {0} исходная строка {1}\",\" используйте {0}, чтобы открыть справку по специальным возможностям.\",\"Копировать удаленные строки\",\"Копировать удаленную строку\",\"Копировать измененные строки\",\"Копировать измененную строку\",\"Копировать удаленную строку ({0})\",\"Копировать измененную строку ({0})\",\"Отменить это изменение\",\"Использовать встроенное представление при ограниченном пространстве\",\"Показать перемещенные блоки кода\",\"Обратить блокировку\",\"Обратить выделение\",\"Открыть средство просмотра с поддержкой специальных возможностей инструмента сравнений\",\"Свернуть неизмененную область\",\"Скрытые строки ({0})\",\"Щелкните или перетащите, чтобы показать больше выше\",\"Показать неизмененный регион\",\"Щелкните или перетащите, чтобы показать больше ниже\",\"Скрытые строки ({0})\",\"Дважды щелкните, чтобы развернуть\",\"Код перемещен с изменениями в строку {0}-{1}\",\"Код перемещен с изменениями из строки {0}-{1}\",\"Код перемещен в строку {0}-{1}\",\"Код перемещен из строки {0}-{1}\",\"Отменить выбранные изменения\",\"Отменить изменение\",\"Цвет границы для текста, перемещенного в редакторе несовпадений.\",\"Цвет активной границы для текста, перемещенного в редакторе несовпадений.\",\"Цвет тени вокруг мини-приложений неизмененных областей.\",\"Оформление строки для вставок в редакторе несовпадений.\",\"Оформление строки для удалений в редакторе несовпадений.\",\"Цвет фона заголовка редактора различий\",\"Цвет фона редактора несовпадений нескольких файлов\",\"Цвет границы редактора различий нескольких файлов\",\"Нет измененных файлов\",\"Редактор\",\"Число пробелов, соответствующее табуляции. Этот параметр переопределяется на основе содержимого файла, если включен параметр {0}.\",\"Число пробелов, используемых для отступа, либо `\\\"tabSize\\\"` для использования значения из \\\"#editor.tabSize#\\\". Этот параметр переопределяется на основе содержимого файла, если включен параметр \\\"#editor.detectIndentation#\\\".\",\"Вставлять пробелы при нажатии клавиши TAB. Этот параметр переопределяется на основе содержимого файла, если включен параметр {0}.\",\"На основе содержимого файла определяет, будут ли {0} и {1} автоматически обнаружены при открытии файла.\",\"Удалить автоматически вставляемый конечный пробел.\",\"Специальная обработка для больших файлов с отключением некоторых функций, которые интенсивно используют память.\",\"Отключите предложения на основе слов.\",\"Предложение слов только из активного документа.\",\"Предложение слов из всех открытых документов на одном языке.\",\"Предложение слов из всех открытых документов.\",\"Определяет, следует ли вычислять завершения на основе слов в документе и из каких документов они вычисляются.\",\"Семантическое выделение включено для всех цветовых тем.\",\"Семантическое выделение отключено для всех цветовых тем.\",\"Семантическое выделение настраивается с помощью параметра \\\"semanticHighlighting\\\" текущей цветовой темы.\",\"Определяет показ семантической подсветки для языков, поддерживающих ее.\",\"Оставлять быстрый редактор открытым даже при двойном щелчке по его содержимому и при нажатии ESC.\",\"Строки, длина которых превышает указанное значение, не будут размечены из соображений производительности\",\"Определяет, должна ли разметка происходить асинхронно в рабочей роли.\",\"Определяет, следует ли регистрировать асинхронную разметку. Только для отладки.\",\"Определяет, должна ли асинхронная разметка проверяться по отношению к устаревшей фоновой разметке. Может замедлить разметку. Только для отладки.\",\"Определяет, следует ли включить синтаксический анализ tree sitter и собирать данные телеметрии. Параметр \\\"editor.experimental.preferTreeSitter\\\" для определенных языков обладает приоритетом.\",\"Определяет символы скобок, увеличивающие или уменьшающие отступ.\",\"Открывающий символ скобки или строковая последовательность.\",\"Закрывающий символ скобки или строковая последовательность.\",\"Определяет пары скобок, цвет которых зависит от их уровня вложения, если включена опция выделения цветом.\",\"Открывающий символ скобки или строковая последовательность.\",\"Закрывающий символ скобки или строковая последовательность.\",\"Время ожидания в миллисекундах, по истечении которого вычисление несовпадений отменяется. Укажите значение 0, чтобы не использовать время ожидания.\",\"Максимальный размер файла в МБ для вычисления различий. Используйте 0 без ограничений.\",\"Определяет, как редактор несовпадений отображает отличия: рядом или в тексте.\",\"Если ширина редактора несовпадений меньше этого значения, используется встроенное представление.\",\"Если параметр включен и ширина редактора слишком мала, используется встроенное представление.\",\"Если этот параметр включен, в редакторе несовпадений на поле глифа отображаются стрелки для отмены изменений.\",\"Если этот параметр включен, в редакторе несовпадений будет отображаться особая панель для действий отмены изменений и подготовки.\",\"Когда параметр включен, редактор несовпадений игнорирует изменения начального или конечного пробела.\",\"Определяет, должны ли в редакторе отображаться индикаторы +/- для добавленных или удаленных изменений.\",\"Определяет, отображается ли CodeLens в редакторе.\",\"Строки не будут переноситься никогда.\",\"Строки будут переноситься по ширине окна просмотра.\",\"Строки будут переноситься в соответствии с настройкой {0}.\",\"Использует устаревший алгоритм сравнения.\",\"Использует расширенный алгоритм сравнения.\",\"Определяет, отображает ли редактор несовпадений неизмененные области.\",\"Определяет, сколько строк используется для неизмененных областей.\",\"Определяет, сколько строк используется в качестве минимального значения для неизмененных областей.\",\"Определяет, сколько строк используется в качестве контекста при сравнении неизмененных областей.\",\"Определяет, должен ли редактор несовпадений показывать обнаруженные перемещения кода.\",\"Определяет, отображает ли редактор несовпадений пустые элементы оформления, чтобы увидеть, где вставлены или удалены символы.\",\"Если этот параметр включен и редактор использует встроенное представление, изменения слов отображаются встроенно.\",\"Использовать API-интерфейсы платформы, чтобы определять, подключено ли средство чтения с экрана.\",\"Оптимизировать для использования со средством чтения с экрана.\",\"Предполагать, что средство чтения с экрана не подключено.\",\"Определяет, следует ли запустить пользовательский интерфейс в режиме оптимизации для средства чтения с экрана.\",\"Определяет, вставляется ли пробел при комментировании.\",\"Определяет, должны ли пустые строки игнорироваться с помощью действий переключения, добавления или удаления для комментариев к строкам.\",\"Управляет тем, копируется ли текущая строка при копировании без выделения.\",\"Определяет, должен ли курсор перемещаться для поиска совпадений при вводе.\",\"Никогда не вставлять начальные значения в строку поиска из выделенного фрагмента редактора.\",\"Всегда вставлять начальные значения в строку поиска из выделенного фрагмента редактора, включая слова в позиции курсора.\",\"Вставлять начальные значения в строку поиска только из выделенного фрагмента редактора.\",\"Определяет, можно ли передать строку поиска в мини-приложение поиска из текста, выделенного в редакторе.\",\"Никогда не включать функцию «Найти в выделении» автоматически (по умолчанию).\",\"Всегда включать функцию «Найти в выделении» автоматически.\",\"Автоматическое включение функции «Найти в выделении» при выборе нескольких строк содержимого.\",\"Управляет условием автоматического включения функции «Найти в выделении».\",\"Определяет, должно ли мини-приложение поиска считывать или изменять общий буфер обмена поиска в macOS.\",\"Определяет, должно ли мини-приложение поиска добавлять дополнительные строки в начале окна редактора. Если задано значение true, вы можете прокрутить первую строку при отображаемом мини-приложении поиска.\",\"Определяет, будет ли поиск автоматически перезапускаться с начала (или с конца), если не найдено никаких других соответствий.\",\"Включает или отключает лигатуры шрифтов (характеристики шрифта \\\"calt\\\" и \\\"liga\\\"). Измените этот параметр на строку для детального управления свойством CSS \\\"font-feature-settings\\\".\",\"Явное свойство CSS \\\"font-feature-settings\\\". Если необходимо только включить или отключить лигатуры, вместо него можно передать логическое значение.\",\"Настраивает лигатуры или характеристики шрифта. Можно указать логическое значение, чтобы включить или отключить лигатуры, или строку для значения свойства CSS \\\"font-feature-settings\\\".\",\"Включает или отключает преобразование из параметра font-weight в font-variation-settings. Измените этот параметр на строку для детального управления свойством CSS font-variation-settings.\",\"Явное свойство CSS font-variation-settings. Если необходимо лишь преобразовать параметр font-weight в параметр font-variation-settings, вместо этого свойства можно передать логическое значение.\",\"Настраивает варианты шрифтов. Может представлять собой логическое значение для включения или отключения преобразования из параметра font-weight в параметр font-variation-settings или строку, содержащую значение свойства CSS font-variation-settings.\",\"Определяет размер шрифта в пикселях.\",\"Допускаются только ключевые слова \\\"normal\\\" или \\\"bold\\\" и числа в диапазоне от 1 до 1000.\",\"Управляет насыщенностью шрифта. Допустимые значения: ключевые слова \\\"normal\\\" или \\\"bold\\\", а также числа в диапазоне от 1 до 1000.\",\"Показать предварительные результаты (по умолчанию)\",\"Перейти к основному результату и показать быстрый редактор\",\"Перейти к основному результату и включить быструю навигацию для остальных\",\"Этот параметр устарел. Используйте вместо него отдельные параметры, например, 'editor.editor.gotoLocation.multipleDefinitions' или 'editor.editor.gotoLocation.multipleImplementations'.\",\"Управляет поведением команды \\\"Перейти к определению\\\" при наличии нескольких целевых расположений.\",\"Управляет поведением команды \\\"Перейти к определению типа\\\" при наличии нескольких целевых расположений.\",\"Управляет поведением команды \\\"Перейти к объявлению\\\" при наличии нескольких целевых расположений.\",\"Управляет поведением команды \\\"Перейти к реализациям\\\" при наличии нескольких целевых расположений.\",\"Управляет поведением команды \\\"Перейти к ссылкам\\\" при наличии нескольких целевых расположений.\",\"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом операции \\\"Перейти к определению\\\" является текущее расположение.\",\"Идентификатор альтернативной команды, которая выполняется в том случае, если результатом операции \\\"Перейти к определению типа\\\" является текущее расположение.\",\"Идентификатор альтернативный команды, выполняемой в том случае, когда результатом операции \\\"Перейти к объявлению\\\" является текущее расположение.\",\"Идентификатор альтернативный команды, выполняемой, когда результатом команды \\\"Перейти к реализации\\\" является текущее расположение.\",\"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом выполнения операции \\\"Перейти к ссылке\\\" является текущее расположение.\",\"Управляет тем, отображается ли наведение.\",\"Определяет время задержки в миллисекундах перед отображением наведения.\",\"Управляет тем, должно ли наведение оставаться видимым при наведении на него курсора мыши.\",\"Определяет время задержки в миллисекундах перед скрытием наведения. Требуется включить \\\"editor.hover.sticky\\\".\",\"Предпочитать отображать наведение над строкой, если есть место.\",\"Предполагает, что все символы имеют одинаковую ширину. Это быстрый алгоритм, который работает правильно для моноширинных шрифтов и некоторых скриптов (например, латинских символов), где глифы имеют одинаковую ширину.\",\"Делегирует вычисление точек переноса браузеру. Это медленный алгоритм, который может привести к зависаниям при обработке больших файлов, но работает правильно во всех случаях.\",\"Управляет алгоритмом, который вычисляет точки переноса. Обратите внимание, что в режиме специальных возможностей будет использован расширенный алгоритм, чтобы обеспечить наибольшее удобство работы.\",\"Отключить меню действий кода.\",\"Показывать меню действий кода, если курсор находится в строках с кодом.\",\"Показывать меню действий кода, если курсор находится на строках с кодом или на пустых строках.\",\"Включает значок лампочки для действия кода в редакторе.\",\"Отображает вложенные текущие области во время прокрутки в верхней части редактора.\",\"Определяет максимальное число залипающих линий для отображения.\",\"Определяет модель, используемую для определения строк залипания. Если модель структуры не существует, она откатится к модели поставщика свертывания, которая откатывается к модели отступов. Этот порядок соблюдается во всех трех случаях.\",\"Включить прокрутку Sticky Scroll с помощью горизонтальной полосы прокрутки редактора.\",\"Включает встроенные указания в редакторе.\",\"Вложенные подсказки включены.\",\"Вложенные подсказки отображаются по умолчанию и скрываются удержанием клавиш {0}.\",\"Вложенные подсказки по умолчанию скрыты и отображаются при удержании {0}.\",\"Вложенные подсказки отключены.\",\"Управляет размером шрифта вложенных подсказок в редакторе. По умолчанию {0} используется, когда сконфигурированное значение меньше {1} или больше размера шрифта редактора.\",\"Управляет семейством шрифтов для вложенных подсказок в редакторе. Если значение не задано, используется {0}.\",\"Включает поля вокруг встроенных указаний в редакторе.\",\"Определяет высоту строки. \\r\\n– Используйте 0, чтобы автоматически вычислить высоту строки на основе размера шрифта.\\r\\n– Значения от 0 до 8 будут использоваться в качестве множителя для размера шрифта.\\r\\n– Значения больше или равные 8 будут использоваться в качестве действующих значений.\",\"Определяет, отображается ли мини-карта.\",\"Определяет, скрыта ли мини-карта автоматически.\",\"Мини-карта имеет такой же размер, что и содержимое редактора (возможна прокрутка).\",\"Мини-карта будет растягиваться или сжиматься по мере необходимости, чтобы заполнить редактор по высоте (без прокрутки).\",\"Миникарта будет уменьшаться по мере необходимости, чтобы никогда не быть больше, чем редактор (без прокрутки).\",\"Управляет размером миникарты.\",\"Определяет, с какой стороны будет отображаться мини-карта.\",\"Определяет, когда отображается ползунок мини-карты.\",\"Масштаб содержимого, нарисованного на мини-карте: 1, 2 или 3.\",\"Отображает фактические символы в строке вместо цветных блоков.\",\"Ограничивает ширину мини-карты, чтобы количество отображаемых столбцов не превышало определенное количество.\",\"Определяет, отображаются ли именованные области в виде заголовков разделов на мини-карте.\",\"Определяет, отображаются ли комментарии \\\"MARK:\\\" в виде заголовков разделов на мини-карте.\",\"Определяет размер шрифта заголовков разделов на мини-карте.\",\"Управляет объемом пространства (в пикселях) между символами заголовка раздела. Это улучшает удобочитаемость заголовка при небольших размерах шрифтов.\",\"Задает пространство между верхним краем редактора и первой строкой.\",\"Задает пространство между нижним краем редактора и последней строкой.\",\"Включает всплывающее окно с документацией по параметру и сведениями о типе, которое отображается во время набора.\",\"Определяет, меню подсказок остается открытым или закроется при достижении конца списка.\",\"Экспресс-предложения отображаются в мини-приложении рекомендаций\",\"Экспресс-предложения отображаются как едва различимый текст\",\"Экспресс-предложения отключены\",\"Разрешение кратких предложений в строках.\",\"Разрешение кратких предложений в комментариях.\",\"Разрешение кратких предложений вне строк и комментариев.\",\"Определяет, должны ли предложения автоматически отображаться при вводе. Этот параметр можно выбрать при вводе примечаний, строк и другого кода. Быстрые предложения можно настроить для отображения в виде фантомного текста или в мини-приложении предложений. Необходимо также помнить о параметре {0}, который управляет активированием предложений специальными символами.\",\"Номера строк не отображаются.\",\"Отображаются абсолютные номера строк.\",\"Отображаемые номера строк вычисляются как расстояние в строках до положения курсора.\",\"Номера строк отображаются каждые 10 строк.\",\"Управляет отображением номеров строк.\",\"Число моноширинных символов, при котором будет отрисовываться линейка этого редактора.\",\"Цвет линейки этого редактора.\",\"Отображать вертикальные линейки после определенного числа моноширинных символов. Для отображения нескольких линеек укажите несколько значений. Если не указано ни одного значения, вертикальные линейки отображаться не будут.\",\"Вертикальная полоса прокрутки будет видна только при необходимости.\",\"Вертикальная полоса прокрутки всегда будет видна.\",\"Вертикальная полоса прокрутки всегда будет скрыта.\",\"Управляет видимостью вертикальной полосы прокрутки.\",\"Горизонтальная полоса прокрутки будет видна только при необходимости.\",\"Горизонтальная полоса прокрутки всегда будет видна.\",\"Горизонтальная полоса прокрутки всегда будет скрыта.\",\"Управляет видимостью горизонтальной полосы прокрутки.\",\"Ширина вертикальной полосы прокрутки.\",\"Высота горизонтальной полосы прокрутки.\",\"Управляет прокруткой при нажатии страницы или переходом к позиции щелчка.\",\"При установке этого параметра горизонтальная полоса прокрутки не будет увеличивать размер содержимого редактора.\",\"Управляет выделением всех нестандартных символов ASCII. Базовыми ASCII считаются только символы между U+0020 и U+007E, табуляция, перевод строки и возврат каретки.\",\"Определяет, выделяются ли символы, которые просто резервируют пространство или вообще не имеют ширины.\",\"Управляет выделением символов, которые можно спутать с основными символами ASCII, кроме тех, которые являются общими в текущем языковом стандарте пользователя.\",\"Определяет, должны ли символы в комментариях также выделяться в Юникоде.\",\"Определяет, должны ли символы в строках также выделяться в Юникоде.\",\"Определяет разрешенные символы, которые не выделяются.\",\"Символы Юникода, распространенные в разрешенных языках, не выделяются.\",\"Определяет, следует ли автоматически показывать встроенные предложения в редакторе.\",\"Отображать панель инструментов встроенного предложения при каждом отображении встроенного предложения.\",\"Отображать панель инструментов предложений при наведении указателя мыши на встроенное предложение.\",\"Не показывать встроенную панель инструментов с предложениями.\",\"Определяет, когда отображать встроенную панель инструментов предложений.\",\"Управляет взаимодействием встроенных предложений с мини-приложением предложений. Если этот параметр включен, мини-приложение предложений не отображается автоматически, когда доступны встроенные предложения.\",\"Управляет семейством шрифтов встроенных предложений.\",null,null,null,null,null,null,\"Определяет, включена ли раскраска пар скобок. Используйте {0} для переопределения цветов выделения скобок.\",\"Определяет, имеет ли каждый тип скобок собственный независимый пул цветов.\",\"Включение направляющих для пар скобок.\",\"Включение направляющих для пар скобок только для активной пары скобок.\",\"Отключение направляющих для пар скобок.\",\"Определяет, включены ли направляющие пар скобок.\",\"Включение горизонтальных направляющих в дополнение к вертикальным направляющим для пар скобок.\",\"Включение горизонтальных направляющих только для активной пары скобок.\",\"Отключение горизонтальных направляющих для пар скобок.\",\"Определяет, включены ли горизонтальные направляющие для скобок.\",\"Управляет тем, должна ли выделяться активная пара квадратных скобок в редакторе.\",\"Определяет, должны ли в редакторе отображаться направляющие отступа.\",\"Выделяет активную направляющую отступа.\",\"Выделяет активную направляющую отступа, даже если выделены направляющие скобок.\",\"Не выделять активную направляющую отступа.\",\"Управляет тем, должна ли выделяться активная направляющая отступа в редакторе.\",\"Вставить предложение без перезаписи текста справа от курсора.\",\"Вставить предложение и перезаписать текст справа от курсора.\",\"Определяет, будут ли перезаписываться слова при принятии вариантов завершения. Обратите внимание, что это зависит от расширений, использующих эту функцию.\",\"Управляет тем, допускаются ли небольшие опечатки в предложениях фильтрации и сортировки.\",\"Определяет, следует ли учитывать при сортировке слова, расположенные рядом с курсором.\",\"Определяет, используются ли сохраненные варианты выбора предложений совместно несколькими рабочими областями и окнами (требуется \\\"#editor.suggestSelection#\\\").\",\"Всегда выбирать предложение при автоматической активации IntelliSense.\",\"Никогда не выбирать предложение при автоматической активации IntelliSense.\",\"Выбирать предложение только при активации IntelliSense с помощью триггерного символа.\",\"Выбирать предложение только при активации IntelliSense по мере ввода.\",\"Определяет, выбирается ли предложение при отображении мини-приложения. Обратите внимание, что этот параметр применяется только к автоматически активированным предложениям ({0} и {1}) и что предложение всегда выбирается при явном вызове, например с помощью клавиш CTRL+ПРОБЕЛ.\",\"Определяет, запрещает ли активный фрагмент кода экспресс-предложения.\",\"Указывает, нужно ли отображать значки в предложениях.\",\"Определяет видимость строки состояния в нижней части виджета предложений.\",\"Определяет, следует ли просматривать результат предложения в редакторе.\",\"Определяет, отображаются ли сведения о предложении в строке вместе с меткой или только в мини-приложении сведений.\",\"Этот параметр является нерекомендуемым. Теперь размер мини-приложения предложений можно изменить.\",\"Этот параметр устарел. Используйте вместо него отдельные параметры, например, 'editor.suggest.showKeywords' или 'editor.suggest.showSnippets'.\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"method\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"function\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"constructor\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"deprecated\\\".\",\"При включении фильтрации IntelliSense необходимо, чтобы первый символ совпадал в начале слова, например \\\"c\\\" в \\\"Console\\\" или \\\"WebContext\\\", но _не_ в \\\"description\\\". Если параметр отключен, IntelliSense отображает больше результатов, но по-прежнему сортирует их по качеству соответствия.\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"field\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"variable\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"class\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"struct\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"interface\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"module\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"property\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"event\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"operator\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"unit\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"value\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"constant\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"enum\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"enumMember\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"keyword\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"text\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"color\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"file\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"reference\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"customcolor\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"folder\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"typeParameter\\\".\",\"Когда параметр включен, в IntelliSense отображаются предложения \\\"snippet\\\".\",\"Во включенном состоянии IntelliSense показывает предложения типа \\\"пользователи\\\".\",\"Во включенном состоянии IntelliSense отображает предложения типа \\\"проблемы\\\".\",\"Должны ли всегда быть выбраны начальный и конечный пробелы.\",\"Следует ли выбирать вложенные слова (например, \\\"foo\\\" в \\\"fooBar\\\" или \\\"foo_bar\\\").\",\"Языки, используемые для сегментации слов при навигации, связанной со словами, или при выполнении операций, связанных со словами. Укажите тег BCP 47 языка распознаваемого слова (пример: ja, zh-CN, zh-Hant-TW, и т. д.).\",\"Языки, используемые для сегментации слов при навигации, связанной со словами, или при выполнении операций, связанных со словами. Укажите тег BCP 47 языка распознаваемого слова (пример: ja, zh-CN, zh-Hant-TW, и т. д.).\",\"Без отступа. Перенос строк начинается со столбца 1.\",\"Перенесенные строки получат тот же отступ, что и родительская строка.\",\"Перенесенные строки получат отступ, увеличенный на единицу по сравнению с родительской строкой. \",\"Перенесенные строки получат отступ, увеличенный на два по сравнению с родительской строкой.\",\"Управляет отступом строк с переносом по словам.\",\"Управляет возможностью перетаскивания файла в текстовый редактор при удерживании клавиши \\\"Shift\\\" (вместо открытия файла в редакторе).\",\"Определяет, отображается ли мини-приложение при сбросе файлов в редактор. Это мини-приложение позволяет управлять тем, как сбрасывается файл.\",\"Отображать мини-приложение выбора сброса после сброса файла в редактор.\",\"Никогда не показывать мини-приложение выбора сброса. Вместо этого всегда используется поставщик сброса по умолчанию.\",\"Определяет, можно ли вставлять содержимое различными способами.\",\"Определяет, отображается ли мини-приложение при вставке содержимого в редактор. Это мини-приложение позволяет управлять тем, как вставляется файл.\",\"Отображать мини-приложение выбора вставки после вставки содержимого в редактор.\",\"Никогда не показывать мини-приложение выбора вставки. Вместо этого всегда используется действие вставки по умолчанию.\",\"Определяет, будут ли предложения приниматься при вводе символов фиксации. Например, в JavaScript точка с запятой (\\\";\\\") может быть символом фиксации, при вводе которого предложение принимается.\",\"Принимать предложение при нажатии клавиши ВВОД только в том случае, если оно изменяет текст.\",\"Определяет, будут ли предложения приниматься клавишей ВВОД в дополнение к клавише TAB. Это помогает избежать неоднозначности между вставкой новых строк и принятием предложений.\",\"Управляет числом строк в редакторе, которые могут быть прочитаны средством чтения с экрана за один раз. При обнаружении средства чтения с экрана автоматически устанавливается значение по умолчанию 500. Внимание! При указании числа строк, превышающего значение по умолчанию, возможно снижение производительности.\",\"Содержимое редактора\",\"Управляйте тем, объявляются ли встроенные предложения средством чтения экрана.\",\"Использовать конфигурации языка для автоматического закрытия скобок.\",\"Автоматически закрывать скобки только в том случае, если курсор находится слева от пробела.\",\"Определяет, должен ли редактор автоматически добавлять закрывающую скобку при вводе пользователем открывающей скобки.\",\"Использовать конфигурации языка для автоматического закрытия комментариев.\",\"Автоматически закрывать комментарии только в том случае, если курсор находится слева от пробела.\",\"Определяет, должен ли редактор автоматически закрывать комментарии при добавлении пользователем открывающего комментария.\",\"Удалять соседние закрывающие кавычки и квадратные скобки только в том случае, если они были вставлены автоматически.\",\"Определяет, должен ли редактор удалять соседние закрывающие кавычки или квадратные скобки при удалении.\",\"Заменять закрывающие кавычки и скобки при вводе только в том случае, если кавычки или скобки были вставлены автоматически.\",\"Определяет, должны ли в редакторе заменяться закрывающие кавычки или скобки при вводе.\",\"Использовать конфигурации языка для автоматического закрытия кавычек.\",\"Автоматически закрывать кавычки только в том случае, если курсор находится слева от пробела.\",\"Определяет, должен ли редактор автоматически закрывать кавычки, если пользователь добавил открывающую кавычку.\",\"Редактор не будет вставлять отступы автоматически.\",\"Редактор будет сохранять отступ текущей строки.\",\"Редактор будет сохранять отступы текущей строки и учитывать скобки в соответствии с синтаксисом языка.\",\"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки и вызывать специальные правила onEnterRules, определяемые языками.\",\"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки, вызывать специальные правила onEnterRules, определяемые языками и учитывать правила отступа indentationRules, определяемые языками.\",\"Определяет, должен ли редактор автоматически изменять отступы, когда пользователи вводят, вставляют или перемещают текст или изменяют отступы строк.\",\"Использовать конфигурации языка для автоматического обрамления выделений.\",\"Обрамлять с помощью кавычек, а не скобок.\",\"Обрамлять с помощью скобок, а не кавычек.\",\"Определяет, должен ли редактор автоматически обрамлять выделения при вводе кавычек или квадратных скобок.\",\"Эмулировать поведение выделения для символов табуляции при использовании пробелов для отступа. Выделение будет применено к позициям табуляции.\",\"Определяет, отображается ли CodeLens в редакторе.\",\"Управляет семейством шрифтов для CodeLens.\",\"Определяет размер шрифта в пикселях для CodeLens. Если задано значение 0, то используется 90% от размера #editor.fontSize#.\",\"Определяет, должны ли в редакторе отображаться внутренние декораторы цвета и средство выбора цвета.\",\"Показывать палитру при щелчке и при наведении указателя на декоратор цвета\",\"Показывать палитру при наведении указателя на декоратор цвета\",\"Показывать палитру при щелчке декоратора цвета\",\"Управляет условием отображения палитры в декораторе цвета\",\"Управляет максимальным количеством цветовых декораторов, которые можно отрисовать в редакторе одновременно.\",\"Включение того, что выбор с помощью клавиатуры и мыши приводит к выбору столбца.\",\"Определяет, будет ли текст скопирован в буфер обмена с подсветкой синтаксиса.\",\"Управляет стилем анимации курсора.\",\"Плавная анимация курсора отключена.\",\"Плавная анимация курсора включена, только если пользователь перемещает курсор явным жестом.\",\"Плавная анимация курсора всегда включена.\",\"Управляет тем, следует ли включить плавную анимацию курсора.\",\"Управляет стилем курсора в режиме ввода со вставкой.\",\"Определяет минимальное число видимых начальных линий (минимум 0) и конечных линий (минимум 1), окружающих курсор. Этот параметр имеет название \\\"scrollOff\\\" или \\\"scrollOffset\\\" в некоторых других редакторах.\",\"\\\"cursorSurroundingLines\\\" применяется только при запуске с помощью клавиатуры или API.\",\"\\\"cursorSurroundingLines\\\" принудительно применяется во всех случаях.\",\"Определяет, когда следует принудительно применять \\\"#editor.cursorSurroundingLines#\\\".\",\"Управляет шириной курсора, когда для параметра \\\"#editor.cursorStyle#\\\" установлено значение 'line'\",\"Определяет, следует ли редактору разрешить перемещение выделенных элементов с помощью перетаскивания.\",\"Использовать новый метод отрисовки с SVG.\",\"Использовать новый метод отрисовки с символами шрифта.\",\"Использовать стабильный метод отрисовки.\",\"Определяет, отрисовывается ли пробел с использованием нового экспериментального метода.\",\"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.\",\"Определяет, включено ли свертывание кода в редакторе.\",\"Используйте стратегию свертывания для конкретного языка, если она доступна, в противном случае используйте стратегию на основе отступов.\",\"Используйте стратегию свертывания на основе отступов.\",\"Управляет стратегией для вычисления свертываемых диапазонов.\",\"Определяет, должен ли редактор выделять сложенные диапазоны.\",\"Определяет, будет ли редактор автоматически сворачивать диапазоны импорта.\",\"Максимальное количество свертываемых регионов. Увеличение этого значения может привести к снижению скорости отклика редактора, если текущий источник содержит большое количество свертываемых регионов.\",\"Определяет, будет ли щелчок пустого содержимого после свернутой строки развертывать ее.\",\"Определяет семейство шрифтов.\",\"Определяет, будет ли редактор автоматически форматировать вставленное содержимое. Модуль форматирования должен быть доступен и иметь возможность форматировать диапазон в документе.\",\"Управляет параметром, определяющим, должен ли редактор автоматически форматировать строку после ввода.\",\"Управляет отображением вертикальных полей глифа в редакторе. Поля глифа в основном используются для отладки.\",\"Управляет скрытием курсора в обзорной линейке.\",\"Управляет интервалом между буквами в пикселях.\",\"Определяет, включена ли поддержка связанного редактирования в редакторе. В зависимости от языка, связанные символы, например теги HTML, обновляются при редактировании.\",\"Определяет, должен ли редактор определять ссылки и делать их доступными для щелчка.\",\"Выделять соответствующие скобки.\",\"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.\",\"Увеличьте шрифт редактора, используя колесо мыши и удерживая \\\"Cmd\\\".\",\"Изменение размера шрифта в редакторе при нажатой клавише CTRL и движении колесика мыши.\",\"Объединить несколько курсоров, когда они перекрываются.\",\"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.\",\"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.\",\"Модификатор, который будет использоваться для добавления нескольких курсоров с помощью мыши. Жесты мыши \\\"Перейти к определению\\\" и \\\"Открыть ссылку\\\" будут изменены так, чтобы они не конфликтовали c [multicursor modifier](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\"Каждый курсор вставляет одну строку текста.\",\"Каждый курсор вставляет полный текст.\",\"Управляет вставкой, когда число вставляемых строк соответствует числу курсоров.\",\"Управляет максимальным числом курсоров, которые могут одновременно отображаться в активном редакторе.\",\"Не выделяет вхождения.\",\"Выделяет вхождения только в текущем файле.\",\"Экспериментальная функция: выделяет вхождения во всех допустимых открытых файлах.\",\"Определяет, следует ли выделять вхождения в открытых файлах.\",\"Определяет, должна ли отображаться граница на обзорной линейке.\",\"Фокусировка на дереве при открытии обзора\",\"Фокусировка на редакторе при открытии обзора\",\"Определяет, следует ли переключить фокус на встроенный редактор или дерево в виджете обзора.\",\"Определяет, всегда ли жест мышью для перехода к определению открывает мини-приложение быстрого редактирования.\",\"Управляет длительностью задержки (в мс) перед отображением кратких предложений.\",\"Определяет, выполняет ли редактор автоматическое переименование по типу.\",\"Не рекомендуется; используйте вместо этого параметр \\\"editor.linkedEditing\\\".\",\"Определяет, должны ли в редакторе отображаться управляющие символы.\",\"Отображение номера последней строки, когда файл заканчивается новой строкой.\",\"Выделяет поле и текущую строку.\",\"Определяет, должен ли редактор выделять текущую строку.\",\"Определяет, должен ли редактор отрисовывать выделение текущей строки, только когда он находится в фокусе.\",\"Отрисовка пробелов, кроме одиночных пробелов между словами.\",\"Отображать пробелы только в выделенном тексте.\",\"Отображать только конечные пробелы.\",\"Определяет, должны ли в редакторе отображаться пробелы.\",\"Управляет тем, необходимо ли отображать скругленные углы для выделения.\",\"Управляет количеством дополнительных символов, на которое содержимое редактора будет прокручиваться по горизонтали.\",\"Определяет, будет ли содержимое редактора прокручиваться за последнюю строку.\",\"Прокрутка только вдоль основной оси при прокрутке по вертикали и горизонтали одновременно. Предотвращает смещение по горизонтали при прокрутке по вертикали на трекпаде.\",\"Контролирует, следует ли поддерживать первичный буфер обмена Linux.\",\"Определяет, должен ли редактор выделять совпадения, аналогичные выбранному фрагменту.\",\"Всегда показывать свертываемые элементы управления.\",\"Никогда не показывать элементы управления свертыванием и уменьшать размер переплета.\",\"Показывать только элементы управления свертывания, когда указатель мыши находится над переплетом.\",\"Определяет, когда элементы управления свертывания отображаются на переплете.\",\"Управляет скрытием неиспользуемого кода.\",\"Управляет перечеркиванием устаревших переменных.\",\"Отображать предложения фрагментов поверх других предложений.\",\"Отображать предложения фрагментов под другими предложениями.\",\"Отображать предложения фрагментов рядом с другими предложениями.\",\"Не отображать предложения фрагментов.\",\"Управляет отображением фрагментов вместе с другими предложениями и их сортировкой.\",\"Определяет, будет ли использоваться анимация при прокрутке содержимого редактора\",\"Определяет, следует ли предоставлять указание о специальных возможностях пользователям средства чтения с экрана при отображении встроенного завершения.\",\"Размер шрифта для мини-приложения предложений. Если установлено {0}, используется значение {1}.\",\"Высота строки для мини-приложения предложений. Если установлено {0}, используется значение {1}. Минимальное значение — 8.\",\"Определяет, должны ли при вводе триггерных символов автоматически отображаться предложения.\",\"Всегда выбирать первое предложение.\",\"Выбор недавних предложений, если только дальнейший ввод не приводит к использованию одного из них, например \\\"console.| -> console.log\\\", так как \\\"log\\\" недавно использовался для завершения.\",\"Выбор предложений с учетом предыдущих префиксов, использованных для завершения этих предложений, например \\\"co -> console\\\" и \\\"con -> const\\\".\",\"Управляет предварительным выбором предложений при отображении списка предложений.\",\"При использовании дополнения по TAB будет добавляться наилучшее предложение при нажатии клавиши TAB.\",\"Отключить дополнение по TAB.\",\"Вставка дополнений по TAB при совпадении их префиксов. Функция работает оптимально, если параметр \\\"quickSuggestions\\\" отключен.\",\"Включает дополнения по TAB.\",\"Необычные символы завершения строки автоматически удаляются.\",\"Необычные символы завершения строки игнорируются.\",\"Для необычных символов завершения строки запрашивается удаление.\",\"Удалите необычные символы завершения строки, которые могут вызвать проблемы.\",\"Пробелы и табуляции вставляются и удаляются в соответствии с позициями табуляции.\",\"Использовать правило разрыва строк по умолчанию.\",\"Не следует использовать разрывы слов для текста на китайском, японском или корейском языке (CJK). Для других текстов используется обычное поведение.\",\"Управляет правилами разбиения по словам, используемыми для текста на китайском,японском и корейском языке (CJK).\",\"Символы, которые будут использоваться как разделители слов при выполнении навигации или других операций, связанных со словами.\",\"Строки не будут переноситься никогда.\",\"Строки будут переноситься по ширине окна просмотра.\",\"Строки будут переноситься по \\\"#editor.wordWrapColumn#\\\".\",\"Строки будут перенесены по минимальному значению из двух: ширина окна просмотра и \\\"#editor.wordWrapColumn#\\\".\",\"Управляет тем, как следует переносить строки.\",\"Определяет столбец переноса редактора, если значение \\\"#editor.wordWrap#\\\" — \\\"wordWrapColumn\\\" или \\\"bounded\\\".\",\"Определяет, должны ли отображаться встроенные цветовые оформления с использованием поставщика цвета документа по умолчанию.\",\"Определяет, получает ли редактор вкладки или откладывает ли их в рабочую среду для навигации.\",\"Цвет фона для выделения строки в позиции курсора.\",\"Цвет фона границ вокруг строки в позиции курсора.\",\"Цвет фона для выделенных диапазонов, например при использовании функций Quick Open или поиска. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона обводки выделения.\",\"Цвет фона выделенного символа, например, в функциях \\\"Перейти к определению\\\" или \\\"Перейти к следующему/предыдущему символу\\\". Цвет должен быть прозрачным, чтобы не скрывать оформление текста под ним.\",\"Цвет фона для границы вокруг выделенных символов.\",\"Цвет курсора редактора.\",\"Цвет фона курсора редактора. Позволяет настраивать цвет символа, перекрываемого прямоугольным курсором.\",\"Цвет основного курсора редактора при наличии нескольких курсоров.\",\"Цвет фона основного курсора редактора при наличии нескольких курсоров. Дает возможность настраивать цвет символа, перекрываемого блочным курсором.\",\"Цвет дополнительных курсоров редактора при наличии нескольких курсоров.\",\"Цвет фона дополнительных курсоров редактора при наличии нескольких курсоров. Дает возможность настраивать цвет символа, перекрываемого блочным курсором.\",\"Цвет пробелов в редакторе.\",\"Цвет номеров строк редактора.\",\"Цвет направляющих для отступов редактора.\",\"Свойство \\\"editorIndentGuide.background\\\" является нерекомендуемым. Вместо этого используйте \\\"editorIndentGuide.background1\\\".\",\"Цвет активных направляющих для отступов редактора.\",\"Свойство \\\"editorIndentGuide.activeBackground\\\" является нерекомендуемым. Вместо этого используйте \\\"editorIndentGuide.activeBackground1\\\".\",\"Цвет направляющих для отступов редактора (1).\",\"Цвет направляющих для отступов редактора (2).\",\"Цвет направляющих для отступов редактора (3).\",\"Цвет направляющих для отступов редактора (4).\",\"Цвет направляющих для отступов редактора (5).\",\"Цвет направляющих для отступов редактора (6).\",\"Цвет активных направляющих для отступов редактора (1).\",\"Цвет активных направляющих для отступов редактора (2).\",\"Цвет активных направляющих для отступов редактора (3).\",\"Цвет активных направляющих для отступов редактора (4).\",\"Цвет активных направляющих для отступов редактора (5).\",\"Цвет активных направляющих для отступов редактора (6).\",\"Цвет номера активной строки редактора\",\"Параметр 'Id' является устаревшим. Используйте вместо него параметр 'editorLineNumber.activeForeground'.\",\"Цвет номера активной строки редактора\",\"Цвет последней строки редактора, когда editor.renderFinalNewline имеет значение dimmed.\",\"Цвет линейки редактора.\",\"Цвет переднего плана элемента CodeLens в редакторе\",\"Цвет фона парных скобок\",\"Цвет прямоугольников парных скобок\",\"Цвет границы для линейки в окне просмотра.\",\"Цвет фона обзорной линейки редактора.\",\"Цвет фона поля в редакторе. В поле размещаются отступы глифов и номера строк.\",\"Цвет границы для ненужного (неиспользуемого) исходного кода в редакторе.\",\"Непрозрачность ненужного (неиспользуемого) исходного кода в редакторе. Например, \\\"#000000c0\\\" отображает код с непрозрачностью 75 %. В высококонтрастных темах для выделения ненужного кода вместо затенения используйте цвет темы \\\"editorUnnecessaryCode.border\\\".\",\"Цвет границы для едва различимого текста в редакторе.\",\"Цвет переднего плана для едва различимого текста в редакторе.\",\"Цвет фона для едва различимого текста в редакторе.\",\"Цвет маркера обзорной линейки для выделения диапазонов. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет метки линейки в окне просмотра для ошибок.\",\"Цвет метки линейки в окне просмотра для предупреждений.\",\"Цвет метки линейки в окне просмотра для информационных сообщений.\",\"Цвет переднего плана для скобок (1). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана для скобок (2). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана для скобок (3). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана для скобок (4). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана для скобок (5). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана для скобок (6). Требуется включить раскраску парных скобок.\",\"Цвет переднего плана непредвиденных скобок.\",\"Цвет фона неактивных направляющих пар скобок (1). Требуется включить направляющие пар скобок.\",\"Цвет фона неактивных направляющих пар скобок (2). Требуется включить направляющие пар скобок.\",\"Цвет фона неактивных направляющих пар скобок (3). Требуется включить направляющие пар скобок.\",\"Цвет фона неактивных направляющих пар скобок (4). Требуется включить направляющие пар скобок.\",\"Цвет фона неактивных направляющих пар скобок (5). Требуется включить направляющие пар скобок.\",\"Цвет фона неактивных направляющих пар скобок (6). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (1). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (2). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (3). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (4). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (5). Требуется включить направляющие пар скобок.\",\"Цвет фона активных направляющих пар скобок (6). Требуется включить направляющие пар скобок.\",\"Цвет границы, используемый для выделения символов Юникода.\",\"Цвет фона, используемый для выделения символов Юникода.\",\"Находится ли фокус на тексте в редакторе (курсор мигает)\",\"Находится ли фокус на редакторе или на мини-приложении редактора (например, фокус находится на мини-приложении поиска)\",\"Находится ли фокус на редакторе или на поле ввода форматированного текста (курсор мигает)\",\"Является ли редактор доступным только для чтения\",\"Является ли контекст редактором несовпадений\",\"Является ли контекст внедренным редактором несовпадений\",null,\"Свернуты ли все файлы в редакторе множественных несовпадений\",\"Содержит ли редактор несовпадений изменения\",\"Выбран ли перемещенный блок кода для сравнения\",\"Отображается ли средство просмотра с поддержкой специальных возможностей инструмента сравнений\",\"Достигнута ли встроенная точка останова параллельной отрисовки редактора несовпадений\",\"Активен ли встроенный режим\",\"Доступен ли измененный файл для записи в редакторе несовпадений\",\"Доступен ли измененный файл для записи в редакторе несовпадений\",\"URI исходного документа\",\"URI измененного документа\",\"Включен ли параметр \\\"editor.columnSelection\\\"\",\"Есть ли в редакторе выбранный текст\",\"Есть ли в редакторе множественный выбор\",\"Перемещается ли фокус с редактора при нажатии клавиши TAB\",\"Является ли наведение в редакторе видимым\",\"Находится ли в фокусе наведение в редакторе\",\"Находится ли залипание прокрутки в фокусе\",\"Отображается ли залипание прокрутки\",\"Видна ли автономная палитра цветов\",\"Сфокусирована ли автономная палитра цветов\",\"Является ли редактор частью большего редактора (например, записных книжек)\",\"Идентификатор языка редактора\",\"Есть ли в редакторе поставщик элементов завершения\",\"Есть ли в редакторе поставщик действий с кодом\",\"Есть ли в редакторе поставщик CodeLens\",\"Есть ли в редакторе поставщик определений\",\"Есть ли в редакторе поставщик объявлений\",\"Есть ли в редакторе поставщик реализации\",\"Есть ли в редакторе поставщик определений типов\",\"Есть ли в редакторе поставщик наведения\",\"Есть ли в редакторе поставщик выделения документов\",\"Есть ли в редакторе поставщик символов документа\",\"Есть ли в редакторе поставщик ссылок\",\"Есть ли в редакторе поставщик переименования\",\"Есть ли в редакторе поставщик справки по сигнатурам\",\"Есть ли в редакторе поставщик встроенных подсказок\",\"Есть ли в редакторе поставщик форматирования документов\",\"Есть ли в редакторе поставщик форматирования для выделения документов\",\"Есть ли в редакторе несколько поставщиков форматирования документов\",\"Есть ли в редакторе несколько поставщиков форматирования для выделения документов\",\"массив\",\"логическое значение\",\"класс\",\"константа\",\"конструктор\",\"перечисление\",\"элемент перечисления\",\"событие\",\"поле\",\"файл\",\"функция\",\"интерфейс\",\"ключ\",\"метод\",\"модуль\",\"пространство имен\",\"NULL\",\"число\",\"объект\",\"оператор\",\"пакет\",\"свойство\",\"строка\",\"структура\",\"параметр типа\",\"Переменная\",\"{0} ({1})\",\"Простой текст\",\"Ввод\",\"Разработчик: проверить токены\",\"Перейти к строке/столбцу...\",\"Показать всех поставщиков быстрого доступа\",\"Палитра команд\",\"Показать и выполнить команды\",\"Перейти к символу...\",\"Перейти к символу по категориям...\",\"Содержимое редактора\",\"Переключить высококонтрастную тему\",\"Внесено изменений в файлах ({1}): {0}.\",\"Показать больше ({0})\",\"Символы: {0}\",\"Начальная точка выделения\",\"Начальная точка установлена в {0}:{1}\",\"Установить начальную точку выделения\",\"Перейти к начальной точке выделения\",\"Выделить текст от начальной точки выделения до курсора\",\"Отменить начальную точку выделения\",\"Цвет метки линейки в окне просмотра для пар скобок.\",\"Перейти к скобке\",\"Выбрать скобку\",\"Удалить скобки\",\"Перейти к &&скобке\",\"Выберите текст внутри, включая скобки или фигурные скобки.\",\"Переместить выделенный текст влево\",\"Переместить выделенный текст вправо\",\"Транспортировать буквы\",\"&&Вырезать\",\"Вырезать\",\"Вырезать\",\"Вырезать\",\"&&Копировать\",\"Копирование\",\"Копирование\",\"Копирование\",\"&&Вставить\",\"Вставить\",\"Вставить\",\"Вставить\",\"Копировать с выделением синтаксиса\",\"Копировать как\",\"Копировать как\",\"Поделиться\",\"Поделиться\",\"При применении действия кода произошла неизвестная ошибка\",\"Тип запускаемого действия кода.\",\"Определяет, когда применяются возвращенные действия.\",\"Всегда применять первое возвращенное действие кода.\",\"Применить первое действие возвращенного кода, если оно является единственным.\",\"Не применять действия возвращенного кода.\",\"Определяет, следует ли возвращать только предпочтительные действия кода.\",\"Быстрое исправление...\",\"Доступные действия кода отсутствуют\",\"Нет доступных предпочтительных действий кода для \\\"{0}\\\".\",\"Действия кода для \\\"{0}\\\" недоступны\",\"Нет доступных предпочтительных действий кода\",\"Доступные действия кода отсутствуют\",\"Рефакторинг...\",\"Нет доступных предпочтительных рефакторингов для \\\"{0}\\\"\",\"Нет доступного рефакторинга для \\\"{0}\\\"\",\"Нет доступных предпочтительных рефакторингов\",\"Доступные операции рефакторинга отсутствуют\",\"Действие с исходным кодом...\",\"Нет доступных предпочтительных действий источника для '{0}'\",\"Нет доступных исходных действий для \\\"{0}\\\"\",\"Предпочтительные действия источника недоступны\",\"Доступные исходные действия отсутствуют\",\"Организация импортов\",\"Действие для упорядочения импортов отсутствует\",\"Исправить все\",\"Нет доступного действия по общему исправлению\",\"Автоисправление...\",\"Нет доступных автоисправлений\",\"Включить или отключить отображение заголовков групп в меню действий кода.\",\"Включить или отключить отображение ближайшего быстрого исправления в строке, если в этот момент не выполняется диагностика.\",\"Включить активацию {0}, если для {1} установлено значение {2}. Для действий кода должно быть установлено значение {3}, чтобы они срабатывали при изменении окна и фокуса.\",\"Контекст: {0} в строке {1} и столбце {2}.\",\"Скрыть отключенные\",\"Показать отключенные\",\"Дополнительные действия...\",\"Быстрое исправление\",\"Извлечь\",\"Встроенный\",\"Переписать\",\"Переместить\",\"Разместить во фрагменте\",\"Действие с исходным кодом\",\"Значок, который вызывает меню действий кода из переплета, если в редакторе нет места.\",\"Значок, который вызывает меню действий кода из переплета, если в редакторе нет места и доступно быстрое исправление.\",\"Значок, который вызывает меню действий кода из переплета, если в редакторе нет места и доступно ИИ-исправление.\",\"Значок, который вызывает меню действий кода из переплета, если в редакторе нет места и доступно ИИ-исправление и быстрое исправление.\",\"Значок, который вызывает меню действий кода из переплета, если в редакторе нет места и доступно ИИ-исправление и быстрое исправление.\",\"Запустить: {0}\",\"Показать действия кода. Доступно предпочтительное быстрое исправление ({0})\",\"Показать действия кода ({0})\",\"Показать действия кода\",\"Показать команды CodeLens для текущей строки\",\"Выберите команду\",null,null,null,null,null,null,null,null,null,\"Закомментировать или раскомментировать строку\",\"Переключить комментарий &&строки\",\"Закомментировать строку\",\"Раскомментировать строку\",\"Закомментировать или раскомментировать блок\",\"Переключить комментарий &&блока\",\"Мини-карта\",\"Отрисовка символов\",\"Размер по вертикали\",\"Пропорционально\",\"Заполнить\",\"Подогнать\",\"Ползунок\",\"Наведение указателя мыши\",\"Всегда\",\"Показать контекстное меню редактора\",\"Отмена действия курсора\",\"Повтор действия курсора\",\"Тип применяемой правки вставки.\\r\\nЕсли существует несколько правок для этого типа, в редакторе будет отображаться средство выбора. Если правки такого типа отсутствуют, в редакторе будет отображаться сообщение об ошибке.\",\"Вставить как...\",\"Вставить как текст\",\"Отображается ли мини-приложение вставки\",\"Показать параметры вставки...\",\"Изменения вставки для \\\"{0}\\\" не найдены\",\"Разрешение встроенной правки. Нажмите, чтобы отменить\",\"Запуск обработчиков вставки. Щелкните, чтобы отменить и выполнить обычную вставку\",\"Выберите действие вставки\",\"Запуск обработчиков вставки\",\"Вставить обычный текст\",\"Вставить URI\",\"Вставить URI\",\"Вставить пути\",\"Вставить путь\",\"Вставить относительные пути\",\"Вставить относительный путь\",\"Вставить HTML\",null,\"Отображается ли мини-приложение сброса\",\"Показать параметры сброса...\",\"Запускаются обработчики сброса. Щелкните для отмены\",\"Ошибка при разрешении правки \\\"{0}\\\":\\r\\n{1}\",\"Ошибка при применении изменения \\\"{0}\\\":\\r\\n{1}\",\"Выполняются ли в редакторе операции, допускающие отмену, например, \\\"Показать ссылки\\\"\",\"Файл слишком велик для выполнения операции замены всех файлов.\",\"Найти\",\"&&Найти\",\"Найти с аргументами\",\"Найти в выбранном\",\"Найти далее\",\"Найти ранее\",\"Перейти к совпадению...\",\"Нет совпадений. Попробуйте найти что-нибудь другое.\",\"Введите число, чтобы перейти к определенному совпадению (от 1 до {0})\",\"Введите число от 1 до {0}\",\"Введите число от 1 до {0}\",\"Найти следующее выделение\",\"Найти предыдущее выделение\",\"Заменить\",\"&&Заменить\",\"Значок, указывающий, что мини-приложение поиска в редакторе свернуто.\",\"Значок, указывающий, что мини-приложение поиска в редакторе развернуто.\",\"Значок для кнопки \\\"Найти в выбранном\\\" в мини-приложении поиска в редакторе.\",\"Значок для кнопки \\\"Заменить\\\" в мини-приложении поиска в редакторе.\",\"Значок для кнопки \\\"Заменить все\\\" в мини-приложении поиска в редакторе.\",\"Значок для кнопки \\\"Найти ранее\\\" в мини-приложении поиска в редакторе.\",\"Значок для кнопки \\\"Найти далее\\\" в мини-приложении поиска в редакторе.\",\"Поиск и замена\",\"Найти\",\"Найти\",\"Предыдущее совпадение\",\"Следующее совпадение\",\"Найти в выделении\",\"Закрыть\",\"Заменить\",\"Заменить\",\"Заменить\",\"Заменить все\",\"Переключение замены\",\"Отображаются только первые {0} результатов, но все операции поиска выполняются со всем текстом.\",\"{0} из {1}\",\"Результаты отсутствуют\",\"{0} обнаружено\",\"{0} найден для \\\"{1}\\\"\",\"{0} найден для \\\"{1}\\\", в {2}\",\"{0} найден для \\\"{1}\\\"\",\"Теперь при нажатии клавиш CTRL+ВВОД вставляется символ перехода на новую строку вместо замены всего текста. Вы можете изменить сочетание клавиш editor.action.replaceAll, чтобы переопределить это поведение.\",\"Развернуть\",\"Развернуть рекурсивно\",\"Свернуть\",\"Переключить свертывание\",\"Свернуть рекурсивно\",\"Переключить рекурсивное свертывание\",\"Свернуть все блоки комментариев\",\"Свернуть все регионы\",\"Развернуть все регионы\",\"Свернуть все кроме выбранных\",\"Развернуть все кроме выбранных\",\"Свернуть все\",\"Развернуть все\",\"Перейти к родительскому свертыванию\",\"Перейти к предыдущему диапазону сложенных данных\",\"Перейти к следующему диапазону сложенных данных\",\"Создать диапазон свертывания из выделенного фрагмента\",\"Удалить диапазоны свертывания вручную\",\"Уровень папки {0}\",\"Цвет фона за свернутыми диапазонами. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже декоративные элементы.\",\"Цвет свернутого текста после первой строки сложенного диапазона.\",\"Цвет элемента управления свертыванием во внутреннем поле редактора.\",\"Значок для развернутых диапазонов на поле глифов редактора.\",\"Значок для свернутых диапазонов на поле глифов редактора.\",\"Значок для свернутых вручную диапазонов на полях глифа редактора.\",\"Значок для развернутых вручную диапазонов на полях глифа редактора.\",\"Щелкните, чтобы расширить диапазон.\",\"Щелкните, чтобы свернуть диапазон.\",\"Увеличить размер шрифта редактора\",\"Уменьшить размер шрифта редактора\",\"Сбросить размер шрифта редактора\",\"Форматировать документ\",\"Форматировать выделенный фрагмент\",\"Перейти к Следующей Проблеме (Ошибке, Предупреждению, Информации)\",\"Значок для перехода к следующему маркеру.\",\"Перейти к Предыдущей Проблеме (Ошибке, Предупреждению, Информации)\",\"Значок для перехода к предыдущему маркеру.\",\"Перейти к следующей проблеме в файлах (ошибки, предупреждения, информационные сообщения)\",\"Следующая &&проблема\",\"Перейти к предыдущей проблеме в файлах (ошибки, предупреждения, информационные сообщения)\",\"Предыдущая &&проблема\",\"Ошибка\",\"Предупреждение\",\"Информация\",\"Указание\",\"{0} в {1}. \",\"Проблемы: {0} из {1}\",\"Проблемы: {0} из {1}\",\"Цвет ошибки в мини-приложении навигации по меткам редактора.\",\"Фон заголовка ошибки в мини-приложении навигации по меткам редактора.\",\"Цвет предупреждения в мини-приложении навигации по меткам редактора.\",\"Фон заголовка предупреждения в мини-приложении навигации по меткам редактора.\",\"Цвет информационного сообщения в мини-приложении навигации по меткам редактора.\",\"Фон заголовка информационного сообщения в мини-приложении навигации по меткам редактора.\",\"Фон мини-приложения навигации по меткам редактора.\",\"Обзор\",\"Определения\",\"Определение для \\\"{0}\\\" не найдено.\",\"Определения не найдены.\",\"Перейти к &&определению\",\"Объявления\",\"Объявление для \\\"{0}\\\" не найдено.\",\"Объявление не найдено\",\"Перейти к &&объявлению\",\"Объявление для \\\"{0}\\\" не найдено.\",\"Объявление не найдено\",\"Определения типов\",\"Не найдено определение типа для \\\"{0}\\\".\",\"Не найдено определение типа.\",\"Перейти к &&определению типа\",\"Реализации\",\"Не найдена реализация для \\\"{0}\\\".\",\"Не найдена реализация.\",\"Перейти к &&реализациям\",\"Ссылки для \\\"{0}\\\" не найдены\",\"Ссылки не найдены\",\"Перейти к &&ссылкам\",\"Ссылки\",\"Ссылки\",\"Расположения\",\"Нет результатов для \\\"{0}\\\"\",\"Ссылки\",\"Перейти к определению\",\"Открыть определение сбоку\",\"Показать определение\",\"Перейти к объявлению\",\"Просмотреть объявление\",\"Перейти к определению типа\",\"Показать определение типа\",\"Перейти к реализациям\",\"Просмотреть реализации\",\"Перейти к ссылкам\",\"Показать ссылки\",\"Перейти к любому символу\",\"Щелкните, чтобы отобразить определения ({0}).\",\"Открыто ли окно просмотра ссылок, например, \\\"Ссылки для просмотра\\\" или \\\"Определение просмотра\\\"\",\"Загрузка...\",\"{0} ({1})\",\"Ссылок: {0}\",\"{0} ссылка\",\"Ссылки\",\"предварительный просмотр недоступен\",\"Результаты отсутствуют\",\"Ссылки\",\"в {0} в строке {1} в столбце {2}\",\"{0} в {1} в строке {2} в столбце {3}\",\"1 символ в {0}, полный путь: {1}\",\"{0} символов в {1}, полный путь: {2} \",\"Результаты не найдены\",\"Обнаружен 1 символ в {0}\",\"Обнаружено {0} символов в {1}\",\"Обнаружено {0} символов в {1} файлах\",\"Существуют ли расположения символов, к которым можно перейти только с помощью клавиатуры\",\"Символ {0} из {1}, {2} для следующего\",\"Символ {0} из {1}\",\"Увеличить уровень детализации в мини-приложении наведения\",\"Уменьшить уровень детализации в мини-приложении наведения\",\"Показать наведение или перевести на него фокус\",\"При наведении указатель мыши не будет автоматически фокусироваться.\",\"При наведении указателя мыши будет фокусировка произойдет только в том случае, если он уже виден.\",\"Когда он появится, при наведении указателя мыши произойдет автоматическая фокусировка.\",\"Отображать предварительный просмотр определения при наведении курсора мыши\",\"Прокрутить наведение вверх\",\"Прокрутить наведение вниз\",\"Прокрутить наведение влево\",\"Прокрутить наведение вправо\",\"Перейти на страницу вверх в наведении\",\"Перейти на страницу вниз в наведении\",\"Перейти к верхнему наведению\",\"Перейти к нижнему наведению\",\"Показать мини-приложение наведения в редакторе или перенести на него фокус. В этом мини-приложении отображается документация, ссылки и другое содержимое для символа в текущей позиции курсора.\",\"Показывать предварительный просмотр определения в мини-приложении наведения в редакторе.\",\"Прокрутить вверх в редакторе в мини-приложении наведения.\",\"Прокрутить вниз в редакторе в мини-приложении наведения.\",\"Прокрутить влево в редакторе в мини-приложении наведения.\",\"Прокрутить вправо в редакторе в мини-приложении наведения.\",\"Предыдущая страница в редакторе при наведении.\",\"Следующая страница в редакторе при наведении.\",\"Перейти к верхней части редактора в мини-приложении наведения.\",\"Перейти к нижней части редактора в мини-приложении наведения.\",\"Значок для увеличения уровня детализации.\",\"Значок для снижения уровня детализации в мини-приложении наведения.\",\"Загрузка...\",\"Отрисовка приостановлена для длинной строки из соображений производительности. Это можно настроить с помощью параметра editor.stopRenderingLineAfter.\",\"Разметка пропускается для длинных строк из соображений производительности. Это можно настроить с помощью \\\"editor.maxTokenizationLineLength\\\".\",\"Увеличить уровень детализации при наведении ({0})\",\"Увеличить уровень детализации при наведении\",\"Уменьшить уровень детализации при наведении ({0})\",\"Уменьшить уровень детализации при наведении\",\"Просмотреть проблему\",\"Исправления недоступны\",\"Проверка наличия исправлений...\",\"Исправления недоступны\",\"Быстрое исправление...\",\"Преобразовать отступ в пробелы\",\"Преобразовать отступ в шаги табуляции\",\"Настроенный размер шага табуляции\",\"Размер табуляции по умолчанию\",\"Текущий размер табуляции\",\"Выбрать размер шага табуляции для текущего файла\",\"Отступ с использованием табуляции\",\"Отступ с использованием пробелов\",\"Изменить отображаемый размер табуляции\",\"Определение отступа от содержимого\",\"Повторно расставить отступы строк\",\"Повторно расставить отступы для выбранных строк\",\"Преобразовать отступы табуляцией в пробелы.\",\"Преобразовать отступы пробелами в табуляцию.\",\"Использовать табуляцию для отступов.\",\"Использовать пробелы для отступов.\",\"Изменение эквивалента размера пространства для вкладки.\",\"Определение отступа от содержимого.\",\"Повторно применить отступы к строкам редактора.\",\"Повторно применить отступы к выделенным строкам редактора.\",\"Дважды щелкните, чтобы вставить\",\"CMD + щелчок\",\"CTRL + щелчок\",\"OPTION + щелчок\",\"ALT + щелчок\",\"Перейти к определению ({0}), щелкните правой кнопкой мыши для просмотра дополнительных сведений\",\"Перейти к определению ({0})\",\"Выполнить команду\",\"Показывать следующее встроенное предложение\",\"Показать предыдущее встроенное предложение\",\"Активировать встроенное предложение\",\"Принять следующее слово встроенного предложения\",\"Принять слово\",\"Принять следующую строку встроенного предложения\",\"Принять строку\",\"Принять встроенное предложение\",\"Принять\",\"Скрыть встроенное предложение\",\"Всегда отображать панель инструментов\",\"Отображается ли встроенное предложение\",\"Начинается ли встроенное предложение с пробела\",\"Проверяет, не является ли пробел перед встроенной рекомендацией короче, чем текст, вставляемый клавишей TAB\",\"Следует ли подавлять предложения для текущего предложения\",\"Проверить этот аспект в представлении с поддержкой специальных возможностей ({0})\",\"Предложение:\",\"Значок для отображения подсказки следующего параметра.\",\"Значок для отображения подсказки предыдущего параметра.\",\"{0} ({1})\",\"Назад\",\"Далее\",null,null,null,null,null,null,null,null,\"Заменить предыдущим значением\",\"Заменить следующим значением\",\"Развернуть выделение строки\",\"Копировать строку сверху\",\"&&Копировать на строку выше\",\"Копировать строку снизу\",\"Копировать на строку &&ниже\",\"Дублировать выбранное\",\"&&Дублировать выбранное\",\"Переместить строку вверх\",\"Переместить на с&&троку выше\",\"Переместить строку вниз\",\"&&Переместить на строку ниже\",\"Сортировка строк по возрастанию\",\"Сортировка строк по убыванию\",\"Удалить дублирующиеся строки\",\"Удалить конечные символы-разделители\",\"Удалить строку\",\"Увеличить отступ\",\"Уменьшить отступ\",\"Вставить строку выше\",\"Вставить строку ниже\",\"Удалить все слева\",\"Удалить все справа\",\"_Объединить строки\",\"Транспонировать символы вокруг курсора\",\"Преобразовать в верхний регистр\",\"Преобразовать в нижний регистр\",\"Преобразовать в заглавные буквы\",\"Преобразовать в написание с подчеркиваниями\",\"Преобразовать в \\\"верблюжий\\\" стиль\",\"Преобразовать в формат \\\"КаждаяЧастьСПрописнойБуквы\\\"\",\"Преобразовать в кебаб-кейс\",\"Запустить связанное редактирование\",\"Цвет фона при автоматическом переименовании типа редактором.\",\"Не удалось открыть ссылку, так как она имеет неправильный формат: {0}\",\"Не удалось открыть ссылку, у нее отсутствует целевой объект.\",\"Выполнить команду\",\"перейти по ссылке\",\"Кнопка CMD и щелчок левой кнопкой мыши\",\"Кнопка CTRL и щелчок левой кнопкой мыши\",\"Кнопка OPTION и щелчок левой кнопкой мыши\",\"Кнопка ALT и щелчок левой кнопкой мыши\",\"Выполнение команды {0}\",\"Открыть ссылку\",\"Отображается ли сейчас в редакторе внутреннее сообщение\",\"Курсор добавлен: {0}\",\"Курсоры добавлены: {0}\",\"Добавить курсор выше\",\"Добавить курсор &&выше\",\"Добавить курсор ниже\",\"Добавить курсор &&ниже\",\"Добавить курсоры к окончаниям строк\",\"Добавить курсоры в &&окончания строк\",\"Добавить курсоры ниже\",\"Добавить курсоры выше\",\"Добавить выделение в следующее найденное совпадение\",\"Добавить &&следующее вхождение\",\"Добавить выделенный фрагмент в предыдущее найденное совпадение\",\"Добавить &&предыдущее вхождение\",\"Переместить последнее выделение в следующее найденное совпадение\",\"Переместить последний выделенный фрагмент в предыдущее найденное совпадение\",\"Выбрать все вхождения найденных совпадений\",\"Выбрать все &&вхождения\",\"Изменить все вхождения\",\"Фокусировка на следующем курсоре\",\"Фокусируется на следующем курсоре\",\"Фокусировка на предыдущем курсоре\",\"Фокусируется на предыдущем курсоре\",\"Переключить подсказки к параметрам\",\"Значок для отображения подсказки следующего параметра.\",\"Значок для отображения подсказки предыдущего параметра.\",\"{0}, указание\",\"Цвет переднего плана активного элемента в указании параметра.\",\"Встроен ли текущий редактор кода в окно просмотра\",\"Закрыть\",\"Цвет фона области заголовка быстрого редактора.\",\"Цвет заголовка быстрого редактора.\",\"Цвет сведений о заголовке быстрого редактора.\",\"Цвет границ быстрого редактора и массива.\",\"Цвет фона в списке результатов представления быстрого редактора.\",\"Цвет переднего плана узлов строки в списке результатов быстрого редактора.\",\"Цвет переднего плана узлов файла в списке результатов быстрого редактора.\",\"Цвет фона выбранной записи в списке результатов быстрого редактора.\",\"Цвет переднего плана выбранной записи в списке результатов быстрого редактора.\",\"Цвет фона быстрого редактора.\",\"Цвет фона поля в окне быстрого редактора.\",\"Цвет фона залипания прокрутки в окне быстрого редактора.\",\"Цвет выделения совпадений в списке результатов быстрого редактора.\",\"Цвет выделения совпадений в быстром редакторе.\",\"Граница выделения совпадений в быстром редакторе.\",\"Цвет переднего плана для замещающего текста в редакторе.\",\"Чтобы перейти к строке, сначала откройте текстовый редактор.\",\"Перейдите к строке {0} и столбцу {1}.\",\"Перейти к строке {0}.\",\"Текущая строка: {0}, символ: {1}. Введите номер строки между 1 и {2} для перехода.\",\"Текущая строка: {0}, символ: {1}. Введите номер строки для перехода.\",\"Чтобы перейти к символу, сначала откройте текстовый редактор с символьной информацией.\",\"Активный текстовый редактор не предоставляет символьную информацию.\",\"Нет совпадающих символов редактора\",\"Нет символов редактора\",\"Открыть сбоку\",\"Открыть внизу\",\"символы ({0})\",\"свойства ({0})\",\"методы ({0})\",\"функции ({0})\",\"конструкторы ({0})\",\"переменные ({0})\",\"классы ({0})\",\"структуры ({0})\",\"события ({0})\",\"операторы ({0})\",\"интерфейсы ({0})\",\"пространства имен ({0})\",\"пакеты ({0})\",\"параметры типа ({0})\",\"модули ({0})\",\"свойства ({0})\",\"перечисления ({0})\",\"элемента перечисления ({0})\",\"строки ({0})\",\"файлы ({0})\",\"массивы ({0})\",\"числа ({0})\",\"логические значения ({0})\",\"объекты ({0})\",\"ключи ({0})\",\"поля ({0})\",\"константы ({0})\",\"Не удается внести изменения во входные данные только для чтения\",\"Не удается выполнить изменение в редакторе только для чтения\",\"Результаты отсутствуют.\",\"Произошла неизвестная ошибка при определении расположения после переименования\",\"Переименование \\\"{0}\\\" в \\\"{1}\\\"\",\"Переименование {0} в {1}\",\"«{0}» успешно переименован в «{1}». Сводка: {2}\",\"Операции переименования не удалось применить правки\",\"Операции переименования не удалось вычислить правки\",\"Переименовать символ\",\"Включить/отключить возможность предварительного просмотра изменений перед переименованием\",\"Фокусировка на следующем предложении по переименованию\",\"Фокусировка на предыдущем предложении переименования\",\"Отображается ли мини-приложение переименования входных данных\",\"Фокусируется ли мини-приложение переименования входных данных\",\"Нажмите {0} для переименования, {1} для просмотра.\",\"Получено несколько ({0}) предложений по переименованию\",\"Введите новое имя для входных данных и нажмите клавишу ВВОД для подтверждения.\",\"Генерировать новые предложения имен\",\"Отмена\",\"Развернуть выбранный фрагмент\",\"&&Развернуть выделение\",\"Уменьшить выделенный фрагмент\",\"&&Сжать выделение\",\"Находится ли текущий редактор в режиме фрагментов\",\"Указывает, существует ли следующая позиция табуляции в режиме фрагментов\",\"Указывает, существует ли предыдущая позиция табуляции в режиме фрагментов\",\"Перейти к следующему заполнителю...\",\"воскресенье\",\"понедельник\",\"вторник\",\"среда\",\"четверг\",\"пятница\",\"суббота\",\"Вс\",\"Пн\",\"Вт\",\"Ср\",\"Чт\",\"Пт\",\"Сб\",\"Январь\",\"Февраль\",\"Март\",\"Апрель\",\"Май\",\"Июнь\",\"Июль\",\"Август\",\"Сентябрь\",\"Октябрь\",\"Ноябрь\",\"Декабрь\",\"Янв\",\"Фев\",\"Мар\",\"Апр\",\"Май\",\"Июн\",\"Июл\",\"Авг\",\"Сен\",\"Окт\",\"Ноя\",\"Дек\",\"&&Переключить липкую прокрутку редактора\",\"Залипание прокрутки\",\"&&Залипание прокрутки\",\"&&Фокус на залипании прокрутки\",\"Переключить залипание прокрутки редактора\",\"Включение или переключение залипания прокрутки редактора, которая показывает вложенные области в верхней части области просмотра.\",\"Фокус на залипании прокрутки редактора\",\"Выберите следующую линию залипания прокрутки редактора\",\"Выбрать предыдущую строку залипания прокрутки\",\"Перейти к строке залипания прокрутки, которая находится в фокусе\",\"Выберите редактор\",\"Находится ли какое-либо предложение в фокусе\",\"Отображаются ли сведения о предложениях\",\"Существует ли несколько предложений для выбора\",\"Приводит ли вставка текущего предложения к изменению или все уже было введено\",\"Вставляются ли предложения при нажатии клавиши ВВОД\",\"Есть ли у текущего предложения варианты поведения \\\"вставка\\\" и \\\"замена\\\"\",\"Является ли текущее поведение поведением \\\"вставка\\\" или \\\"замена\\\"\",\"Поддерживает ли текущее предложение разрешение дополнительных сведений\",\"Принятие \\\"{0}\\\" привело к внесению дополнительных правок ({1})\",\"Переключить предложение\",\"Вставить\",\"Вставить\",\"Заменить\",\"Заменить\",\"Вставить\",\"Показать меньше\",\"Показать больше\",\"Сброс предложения размера мини-приложения\",\"Цвет фона виджета подсказок.\",\"Цвет границ виджета подсказок.\",\"Цвет переднего плана мини-приложения предложений.\",\"Цвет переднего плана выбранной записи в мини-приложении предложений.\",\"Цвет переднего плана значка выбранной записи в мини-приложении предложений.\",\"Фоновый цвет выбранной записи в мини-приложении предложений.\",\"Цвет выделения соответствия в мини-приложении предложений.\",\"Цвет совпадения выделяется в мини-приложениях предложений, когда элемент находится в фокусе.\",\"Цвет переднего плана для состояния рекомендации мини-приложения.\",\"Загрузка...\",\"Предложения отсутствуют.\",\"Предложить\",\"{0} {1}, {2}\",\"{0} {1}\",\"{0}, {1}\",\"{0}, документы: {1}\",\"Закрыть\",\"Загрузка...\",\"Значок для получения дополнительных сведений в мини-приложении предложений.\",\"Подробнее\",\"Цвет переднего плана для символов массива. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для логических символов. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов класса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов цвета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов константы. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов конструктора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов члена перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов события. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов поля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов файла. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов папки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов функции. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов интерфейса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов ключа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов ключевого слова. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов метода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов модуля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов пространства имен. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов NULL. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов числа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов объекта. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов оператора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов пакета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов свойства. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов ссылки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов фрагмента кода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов строки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов структуры. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов текста. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов типа параметров. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов единиц. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"Цвет переднего плана для символов переменной. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.\",\"При нажатии клавиши TAB фокус перейдет на следующий элемент, который может получить фокус\",\"Теперь при нажатии клавиши TAB будет вставлен символ табуляции\",\"Переключение клавиши TAB перемещает фокус.\",\"Определяет действие при нажатии клавиши TAB: перемещение фокуса по рабочему интерфейсу или вставка знака табуляции в текущем редакторе. Это также называется захватом фокуса на вкладках, навигацией по вкладкам или режимом фокусировки на вкладках.\",\"Разработчик: принудительная повторная установка токенов\",\"Значок, отображаемый с предупреждением в редакторе расширений.\",\"Этот документ содержит много нестандартных символов Юникода ASCII\",\"Этот документ содержит много неоднозначных символов Юникода\",\"Этот документ содержит много невидимых символов Юникода\",\"Настройка параметров выделения Юникода\",\"Символ {0} можно спутать с символом ASCII {1}, который чаще встречается в исходном коде.\",\"Символ {0} можно спутать с символом {1}, который чаще встречается в исходном коде.\",\"Символ {0} невидим.\",\"Символ {0} не является базовым символом ASCII.\",\"Настройка параметров\",\"Отключить выделение в комментариях\",\"Отключить выделение символов в комментариях\",\"Отключить выделение в строках\",\"Отключить выделение символов в строках\",\"Отключить неоднозначное выделение\",\"Отключить выделение неоднозначных символов\",\"Отключить невидимое выделение\",\"Отключить выделение невидимых символов\",\"Отключить выделение, отличное от ASCII\",\"Отключить выделение нестандартных символов ASCII\",\"Показать параметры исключения\",\"Исключить {0} (невидимый символ) из выделения\",\"Исключить {0} из выделения\",\"Разрешите символы Юникода, более распространенные в языке \\\"{0}\\\".\",\"Необычные символы завершения строки\",\"Обнаружены необычные символы завершения строки\",\"Файл \\\"{0}\\\" содержит один или несколько необычных символов завершения строки, таких как разделитель строк (LS) или разделитель абзацев (PS).\\r\\n\\r\\nРекомендуется удалить их из файла. Удаление этих символов можно настроить с помощью параметра \\\"editor.unusualLineTerminators\\\".\",\"&&Удалить необычные символы завершения строки\",\"Пропустить\",\"Цвет фона символа при доступе на чтение, например, при чтении переменной. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона для символа во время доступа на запись, например при записи в переменную. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона текстового вхождения символа. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\"Цвет границы символа при доступе на чтение, например, при считывании переменной.\",\"Цвет границы символа при доступе на запись, например, при записи переменной. \",\"Цвет границы текстового вхождения символа.\",\"Цвет маркера обзорной линейки для выделения символов. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\"Цвет маркера обзорной линейки для выделения символов доступа на запись. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет маркера обзорной линейки текстового вхождения символа. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.\",\"Перейти к следующему выделению символов\",\"Перейти к предыдущему выделению символов\",\"Включить или отключить выделение символов\",\"Удалить слово\",\"Ошибка в позиции\",\"Ошибка\",\"Предупреждение в позиции\",\"Предупреждение\",\"Ошибка в строке\",\"Ошибка в строке\",\"Предупреждение в строке\",\"Предупреждение в строке\",\"Сложенная область в строке\",\"Свернуто\",\"Точка останова в строке\",\"Точка останова\",\"Встроенная рекомендация в строке\",\"Быстрое исправление терминала\",\"Быстрое исправление\",\"Отладчик остановлен в точке останова\",\"Точка останова\",\"Отсутствие встроенных подсказок в строке\",\"Вложенные указания отсутствуют\",\"Задача завершена\",\"Задача завершена\",\"Сбой задачи\",\"Сбой задачи\",\"Сбой команды терминала\",\"Сбой команды\",\"Команда терминала выполнена\",\"Команда выполнена\",\"Колокольчик терминала\",\"Звонок терминала\",\"Ячейка записной книжки выполнена\",\"Ячейка записной книжки выполнена\",\"Сбой ячейки записной книжки\",\"Сбой ячейки записной книжки\",\"Вставлена разностная строка\",\"Удалена разностная строка\",\"Изменена строка различий\",\"Отправлен запрос на чат\",\"Отправлен запрос на чат\",\"Получен ответ чата\",\"Ход выполнения\",\"Ход выполнения\",\"Очистить\",\"Очистка\",\"Сохранить\",\"Сохранить\",\"Формат\",\"Формат\",\"Запись голоса начата\",\"Запись голоса остановлена\",\"Представление\",\"Справка\",\"Тест\",\"Файл\",\"Параметры\",\"Разработчик\",\"{0} ({1})\",\"{0} ({1})\",\"{0}\\r\\n[{1}] {2}\",\"{1} для {0}\",\"{0} ({1})\",\"Скрыть\",\"Сбросить меню\",\"Скрыть \\\"{0}\\\"\",\"Настройка сочетания клавиш\",\"{0}, чтобы применить, {1} для предварительного просмотра\",\"{0}, чтобы применить\",\"{0}, причина отключения: {1}\",\"Мини-приложения действий\",\"Цвет фона для переключаемых элементов действий на панели действий.\",\"Отображается ли список мини-приложений действий\",\"Скрыть мини-приложение действия\",\"Выбрать предыдущее действие\",\"Выбрать следующее действие\",\"Принять выбранное действие\",\"Предварительный просмотр выбранного действия\",\"Переопределения конфигурации языка по умолчанию\",\"Настройка переопределяемых параметров для языка {0}.\",\"Настройка параметров редактора, переопределяемых для языка.\",\"Этот параметр не поддерживает настройку для отдельных языков.\",\"Настройка параметров редактора, переопределяемых для языка.\",\"Этот параметр не поддерживает настройку для отдельных языков.\",\"Не удается зарегистрировать пустое свойство\",\"Невозможно зарегистрировать \\\"{0}\\\". Оно соответствует шаблону свойства '\\\\\\\\[.*\\\\\\\\]$' для описания параметров редактора, определяемых языком. Используйте участие configurationDefaults.\",\"Невозможно зарегистрировать \\\"{0}\\\". Это свойство уже зарегистрировано.\",\"Невозможно зарегистрировать \\\"{0}\\\". Уже имеется регистрация {2} для связанной политики {1}.\",\"Команда, возвращающая сведения о ключах контекста\",\"Пустое выражение ключа контекста\",\"Вы забыли записать выражение? Вы также можете поместить \\\"false\\\" или \\\"true\\\", чтобы всегда оценивать по значению false или true соответственно.\",\"\\\"in\\\" после \\\"not\\\".\",\"закрывающая круглая скобка \\\")\\\"\",\"Непредвиденный маркер\",\"Возможно, вы забыли поместить && или || перед маркером?\",\"Неожиданный конец выражения\",\"Возможно, вы забыли поместить ключ контекста?\",\"Ожидается: {0}\\r\\nПолучено: \\\"{1}\\\".\",\"Используется ли операционная система macOS\",\"Используется ли операционная система Linux\",\"Используется ли операционная система Windows\",\"Является ли платформа браузерной\",\"Используется ли операционная система macOS на платформе, отличной от браузерной\",\"Используется ли операционная система IOS\",\"Является ли платформа мобильным браузером\",\"Тип качества VS Code\",\"Находится ли фокус клавиатуры в поле ввода\",\"Вы имели в виду {0}?\",\"Вы имели в виду {0} или {1}?\",\"Вы имели в виду {0}, {1} или {2}?\",\"Вы забыли открыть или закрыть цитату?\",\"Вы забыли экранировать символ \\\"/\\\" (косая черта)? Чтобы экранировать, поместите перед символом две обратные косые черты, например \\\"\\\\\\\\/\\\".\",\"Отображаются ли предложения\",\"Была нажата клавиша {0}. Ожидание нажатия второй клавиши сочетания...\",\"Была нажата клавиша ({0}). Ожидание нажатия следующей клавиши сочетания...\",\"Сочетание клавиш ({0} и {1}) не является командой.\",\"Сочетание клавиш ({0} и {1}) не является командой.\",\"Рабочее место\",\"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.\",\"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.\",\"Модификатор, который будет использоваться для добавления элементов в деревьях и списках в элемент множественного выбора с помощью мыши (например, в проводнике, в открытых редакторах и в представлении scm). Жесты мыши \\\"Открыть сбоку\\\" (если они поддерживаются) будут изменены таким образом, чтобы они не конфликтовали с модификатором элемента множественного выбора.\",\"Управляет тем, как открывать элементы в деревьях и списках с помощью мыши (если поддерживается). Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.\",\"Определяет, поддерживают ли горизонтальную прокрутку списки и деревья на рабочем месте. Предупреждение! Включение этого параметра может повлиять на производительность.\",\"Определяет, следует ли щелкать полосу прокрутки постранично.\",\"Определяет отступ для дерева в пикселях.\",\"Определяет, нужно ли в дереве отображать направляющие отступа.\",\"Управляет тем, используется ли плавная прокрутка для списков и деревьев.\",\"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.\",\"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.\",\"При поиске необходимо выделять элементы. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.\",\"Фильтруйте элементы при поиске.\",\"Управляет режимом поиска по умолчанию для списков и деревьев в Workbench.\",\"Про простой навигации с клавиатуры выбираются элементы, соответствующие вводимым с клавиатуры данным. Сопоставление осуществляется только по префиксам.\",\"Функция подсветки навигации с клавиатуры выделяет элементы, соответствующие вводимым с клавиатуры данным. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.\",\"Фильтр навигации с клавиатуры позволяет отфильтровать и скрыть все элементы, не соответствующие вводимым с клавиатуры данным.\",\"Управляет стилем навигации с клавиатуры для списков и деревьев в Workbench. Доступен простой режим, режим выделения и режим фильтрации.\",\"Вместо этого используйте \\\"workbench.list.defaultFindMode\\\" и \\\"workbench.list.typeNavigationMode\\\".\",\"Использовать нечеткое соответствие при поиске.\",\"Использовать непрерывное сопоставление при поиске.\",\"Управляет типом сопоставления, используемым при поиске списков и деревьев в Workbench.\",\"Управляет тем, как папки дерева разворачиваются при нажатии на имена папок. Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.\",\"Определяет, включено ли залипание прокрутки в деревьях.\",\"Управляет числом прикрепленных элементов, отображаемых в дереве, когда включен параметр {0}.\",\"Управляет работой навигации по типам в списках и деревьях в рабочей среде. Если установлено значение \\\"trigger\\\", навигация по типу начинается после запуска команды \\\"list.triggerTypeNavigation\\\".\",\"Ошибка\",\"Предупреждение\",\"Информация\",\"недавно использованные\",\"похожие команды\",\"часто используемые\",\"другие команды\",\"похожие команды\",\"{0}, {1}\",\"Команда \\\"{0}\\\" привела к ошибке\",\"{0}, {1}\",\"Находится ли фокус клавиатуры внутри элемента управления быстрым вводом\",\"Тип видимого в данный момент быстрого ввода\",\"Находится ли курсор при быстром вводе в конце поля ввода\",\"Назад\",\"Нажмите клавишу ВВОД, чтобы подтвердить введенные данные, или ESCAPE для отмены\",\"{0} / {1}\",\"Введите текст, чтобы уменьшить число результатов.\",\"Используется в контексте быстрого выбора. Если вы измените одну комбинацию клавиш для этой команды, вам также следует изменить все остальные комбинации клавиш (варианты модификаторов) этой команды.\",\"Если мы находимся в режиме быстрого доступа, это приведет к переходу к следующему элементу. Если мы не находимся в режиме быстрого доступа, это приведет к переходу к следующему разделителю.\",\"Если мы находимся в режиме быстрого доступа, это приведет к переходу к предыдущему элементу. Если мы не находимся в режиме быстрого доступа, это приведет к переходу к предыдущему разделителю.\",\"Переключить все флажки\",\"Результаты: {0}\",\"{0} выбрано\",\"ОК\",\"Другой\",\"Назад ({0})\",\"Назад\",\"Быстрый ввод\",\"Щелкните, чтобы выполнить команду \\\"{0}\\\"\",\"Общий цвет переднего плана. Этот цвет используется, только если его не переопределит компонент.\",\"Общий цвет переднего плана для отключенных элементов. Этот цвет используется только в том случае, если он не переопределен компонентом.\",\"Общий цвет переднего плана для сообщений об ошибках. Этот цвет используется только если его не переопределяет компонент.\",\"Цвет текста элемента, содержащего пояснения, например, для метки.\",\"Цвет по умолчанию для значков на рабочем месте.\",\"Общий цвет границ для элементов с фокусом. Этот цвет используется только в том случае, если не переопределен в компоненте.\",\"Дополнительная граница вокруг элементов, которая отделяет их от других элементов для улучшения контраста.\",\"Дополнительная граница вокруг активных элементов, которая отделяет их от других элементов для улучшения контраста.\",\"Цвет фона выделенного текста в рабочей области (например, в полях ввода или в текстовых полях). Не применяется к выделенному тексту в редакторе.\",\"Цвет переднего плана для ссылок в тексте.\",\"Цвет переднего плана для ссылок в тексте при щелчке и при наведении курсора мыши.\",\"Цвет для разделителей текста.\",\"Цвет текста фиксированного формата.\",\"Цвет фона для сегментов предварительно отформатированного текста.\",\"Цвет фона для блоков с цитатами в тексте.\",\"Цвет границ для блоков с цитатами в тексте.\",\"Цвет фона для программного кода в тексте.\",\"Цвет переднего плана на диаграммах.\",\"Цвет горизонтальных линий на диаграммах.\",\"Красный цвет, используемый в визуализациях диаграмм.\",\"Синий цвет, используемый в визуализациях диаграмм.\",\"Желтый цвет, используемый в визуализациях диаграмм.\",\"Оранжевый цвет, используемый в визуализациях диаграмм.\",\"Зеленый цвет, используемый в визуализациях диаграмм.\",\"Лиловый цвет, используемый в визуализациях диаграмм.\",\"Цвет фона редактора.\",\"Цвет переднего плана редактора по умолчанию.\",\"Цвет фона липкой прокрутки в редакторе\",\"Цвет фона липкой прокрутки при наведении курсора мыши в редакторе\",\"Цвет тени липкой прокрутки в редакторе\",\" Цвет тени липкой прокрутки в редакторе\",\"Цвет фона виджетов редактора, таких как найти/заменить.\",\"Цвет переднего плана мини-приложений редактора, таких как \\\"Поиск/замена\\\".\",\"Цвет границы мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница и если этот цвет не переопределен мини-приложением.\",\"Цвет границы панели изменения размера мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница для изменения размера и если этот цвет не переопределен мини-приложением.\",\"Цвет фона для текста ошибки в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет волнистой линии для выделения ошибок в редакторе.\",\"Если задано, цвет двойного подчеркивания ошибок в редакторе.\",\"Цвет фона для текста предупреждения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет волнистой линии для выделения предупреждений в редакторе.\",\"Если задано, цвет двойного подчеркивания предупреждений в редакторе.\",\"Цвет фона для текста информационного сообщения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет волнистой линии для выделения информационных сообщений в редакторе.\",\"Если задано, цвет двойного подчеркивания информационных сообщений в редакторе.\",\"Цвет волнистой линии для выделения подсказок в редакторе.\",\"Если задано, цвет двойного подчеркивания указаний в редакторе.\",\"Цвет активных ссылок.\",\"Цвет выделения редактора.\",\"Цвет выделенного текста в режиме высокого контраста.\",\"Цвет выделения в неактивном редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет для областей, содержимое которых совпадает с выбранным фрагментом. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет границы регионов с тем же содержимым, что и в выделении.\",\"Цвет текущего поиска совпадений.\",\"Цвет текста текущего результата поиска.\",\"Цвет других совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет переднего плана других результатов поиска.\",\"Цвет диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет границы текущего результата поиска.\",\"Цвет границы других результатов поиска.\",\"Цвет границы для диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Выделение под словом, для которого отображается меню при наведении курсора. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона при наведении указателя на редактор.\",\"Цвет переднего плана для наведения указателя на редактор.\",\"Цвет границ при наведении указателя на редактор.\",\"Цвет фона строки состояния при наведении в редакторе.\",\"Цвет переднего плана встроенных указаний\",\"Цвет фона встроенных указаний\",\"Цвет переднего плана встроенных указаний для шрифтов\",\"Цвет фона встроенных указаний для шрифтов\",\"Цвет переднего плана встроенных указаний для параметров\",\"Цвет фона встроенных указаний для параметров\",\"Цвет, используемый для значка действий в меню лампочки.\",\"Цвет, используемый для значка действий автоматического исправления в меню лампочки.\",\"Цвет, используемый для значка ИИ с лампочкой.\",\"Цвет фона выделения в позиции табуляции фрагмента.\",\"Цвет границы выделения в позиции табуляции фрагмента.\",\"Цвет фона выделения в последней позиции табуляции фрагмента.\",\"Выделение цветом границы в последней позиции табуляции фрагмента.\",\"Цвет фона для вставленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона для удаленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона для вставленных строк. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона для удаленных строк. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет фона для поля, где вставлены строки.\",\"Цвет фона для поля, где удалены строки.\",\"Передний план обзорной линейки различий для вставленного содержимого.\",\"Передний план обзорной линейки различий для удаленного содержимого.\",\"Цвет контура для добавленных строк.\",\"Цвет контура для удаленных строк.\",\"Цвет границы между двумя текстовыми редакторами.\",\"Цвет диагональной заливки для редактора несовпадений. Диагональная заливка используется в размещаемых рядом представлениях несовпадений.\",\"Цвет фона неизмененных блоков в редакторе несовпадений.\",\"Цвет переднего плана неизмененных блоков в редакторе несовпадений.\",\"Цвет фона неизмененного кода в редакторе несовпадений.\",\"Цвет тени мини-приложений редактора, таких как \\\"Найти/заменить\\\".\",\"Цвет границы мини-приложений редактора, таких как \\\"Найти/заменить\\\".\",\"Фон панели инструментов при наведении указателя мыши на действия\",\"Контур панели инструментов при наведении указателя мыши на действия\",\"Фон панели инструментов при удержании указателя мыши над действиями\",\"Цвет элементов навигации, находящихся в фокусе.\",\"Фоновый цвет элементов навигации.\",\"Цвет элементов навигации, находящихся в фокусе.\",\"Цвет выделенных элементов навигации.\",\"Фоновый цвет средства выбора элементов навигации.\",\"Текущий цвет фона заголовка при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Фон текущего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Фон входящего заголовка при внутренних конфликтах объединения. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Фон входящего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Фон заголовка общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Фон содержимого общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет границы заголовков и разделителя во внутренних конфликтах слияния.\",\"Цвет переднего плана линейки текущего окна во внутренних конфликтах слияния.\",\"Цвет переднего плана линейки входящего окна во внутренних конфликтах слияния.\",\"Цвет переднего плана для обзорной линейки для общего предка во внутренних конфликтах слияния. \",\"Цвет маркера обзорной линейки для совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Маркер обзорной линейки для выделения выбранного фрагмента. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.\",\"Цвет, используемый для значка ошибки, указывающего на наличие проблем.\",\"Цвет, используемый для предупреждающего значка, указывающего на наличие проблем.\",\"Цвет, используемый для информационного значка, указывающего на наличие проблем.\",\"Фон поля ввода.\",\"Передний план поля ввода.\",\"Граница поля ввода.\",\"Цвет границ активированных параметров в полях ввода.\",\"Цвет фона активированных параметров в полях ввода.\",\"Цвет фонового наведения параметров в полях ввода.\",\"Цвет переднего плана активированных параметров в полях ввода.\",\"Цвет фона поясняющего текста в элементе ввода.\",\"Фоновый цвет проверки ввода для уровня серьезности \\\"Сведения\\\".\",\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Сведения\\\".\",\"Цвет границы проверки ввода для уровня серьезности \\\"Сведения\\\".\",\"Фоновый цвет проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\"Цвет границы проверки ввода для уровня серьезности \\\"Предупреждение\\\".\",\"Фоновый цвет проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\"Цвет переднего плана области проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\"Цвет границы проверки ввода для уровня серьезности \\\"Ошибка\\\".\",\"Фон раскрывающегося списка.\",\"Цвет фона раскрывающегося списка.\",\"Передний план раскрывающегося списка.\",\"Граница раскрывающегося списка.\",\"Цвет переднего плана кнопки.\",\"Цвет разделителя кнопок.\",\"Цвет фона кнопки.\",\"Цвет фона кнопки при наведении.\",\"Цвет границы кнопки.\",\"Цвет переднего плана вторичной кнопки.\",\"Цвет фона вторичной кнопки.\",\"Цвет фона вторичной кнопки при наведении курсора мыши.\",\"Цвет переднего плана активного переключателя.\",\"Цвет фона активного переключателя.\",\"Цвет границы активного переключателя.\",\"Цвет переднего плана неактивного переключателя.\",\"Цвет фона неактивного переключателя.\",\"Цвет границы неактивного переключателя.\",\"Цвет фона неактивного переключателя при наведении указателя мыши.\",\"Цвет фона мини-приложения флажка.\",\"Цвет фона виджета флажка при выборе элемента, в котором он находится.\",\"Цвет переднего плана мини-приложения флажка.\",\"Цвет границы мини-приложения флажка.\",\"Цвет границы виджета флажка, когда выбран элемент, в котором он находится.\",\"Цвет фона метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\"Цвет переднего плана метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\"Цвет границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\"Цвет нижней границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.\",\"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет переднего плана находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен и выбран. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет переднего плана выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево активны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.\",\"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет текста выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево неактивны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.\",\"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.\",\"Фоновый цвет элементов List/Tree при наведении курсора мыши.\",\"Цвет переднего плана элементов List/Tree при наведении курсора мыши.\",\"Перетаскивание фона списка или дерева при перемещении элементов над другими элементами с помощью мыши.\",\"Цвет границы перетаскивания списка/дерева при перемещении элементов между элементами с помощью мыши.\",\"Цвет переднего плана для выделения соответствия при поиске по элементу List/Tree.\",\"Цвет переднего плана для выделения соответствия выделенных элементов при поиске по элементу List/Tree.\",\"Цвет переднего плана списка/дерева для недопустимых элементов, например, для неразрешенного корневого узла в проводнике.\",\"Цвет переднего плана элементов списка, содержащих ошибки.\",\"Цвет переднего плана элементов списка, содержащих предупреждения.\",\"Цвет фона для мини-приложения фильтра типов в списках и деревьях.\",\"Цвет контура для мини-приложения фильтра типов в списках и деревьях.\",\"Цвет контура для мини-приложения фильтра типов в списках и деревьях при отсутствии совпадений.\",\"Цвет тени для мини-приложения фильтра типов в списках и деревьях.\",\"Цвет фона для отфильтрованного совпадения.\",\"Цвет границы для отфильтрованного совпадения.\",\"Цвет переднего плана в списке или дереве для элементов, выделение которых уменьшено.\",\"Цвет штриха дерева для направляющих отступа.\",\"Цвет штриха дерева для неактивных направляющих отступа.\",\"Цвет границы таблицы между столбцами.\",\"Цвет фона для нечетных строк таблицы.\",\"Цвет фона списка действий.\",\"Цвет переднего плана списка действий.\",\"Цвет переднего плана списка действий для элемента, на котором находится фокус.\",\"Цвет фона списка действий для элемента, на котором находится фокус.\",\"Цвет границ меню.\",\"Цвет переднего плана пунктов меню.\",\"Цвет фона пунктов меню.\",\"Цвет переднего плана выбранного пункта меню в меню.\",\"Цвет фона для выбранного пункта в меню.\",\"Цвет границы для выбранного пункта в меню.\",\"Цвет разделителя меню в меню.\",\"Цвет маркера мини-карты для поиска совпадений.\",\"Цвет маркера мини-карты для повторяющихся выделений редактора.\",\"Цвет маркера мини-карты для выбора редактора.\",\"Цвет маркера на мини-карте для информации.\",\"Цвет маркера миникарты для предупреждений.\",\"Цвет маркера миникарты для ошибок.\",\"Цвет фона мини-карты.\",\"Прозрачность элементов переднего плана, отображаемая га мини-карте. Например, \\\"#000000c0\\\" отображает элементы с прозрачностью 75%.\",\"Цвет фона ползунка мини-карты.\",\"Цвет фона ползунка мини-карты при наведении на него указателя.\",\"Цвет фона ползунка мини-карты при его щелчке.\",\"Цвет границы активных лент.\",\"Цвет фона бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.\",\"Цвет текста бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.\",\"Цвет тени полосы прокрутки, которая свидетельствует о том, что содержимое прокручивается.\",\"Цвет фона для ползунка полосы прокрутки.\",\"Цвет фона ползунка полосы прокрутки при наведении курсора.\",\"Цвет фона ползунка полосы прокрутки при щелчке по нему.\",\"Цвет фона индикатора выполнения, который может отображаться для длительных операций.\",\"Цвет фона для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\"Цвет переднего плана для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\"Цвет фона для заголовка средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.\",\"Цвет средства быстрого выбора для группировки меток.\",\"Цвет средства быстрого выбора для группировки границ.\",\"Рекомендуется использовать quickInputList.focusBackground.\",\"Цвет переднего плана средства быстрого выбора для элемента, на котором находится фокус.\",\"Цвет переднего плана значка средства быстрого выбора для элемента, на котором находится фокус.\",\"Цвет фона средства быстрого выбора для элемента, на котором находится фокус.\",\"Цвет текста в поиске сообщения завершения вьюлета.\",\"Цвет соответствий для запроса в редакторе поиска.\",\"Цвет границы для соответствующих запросов в редакторе поиска.\",\"Этот цвет должен быть прозрачным, иначе он будет скрывать содержимое\",\"Использовать цвет по умолчанию.\",\"Идентификатор используемого шрифта. Если параметр не задан, используется шрифт, определенный первым.\",\"Символ шрифта, связанный с определением значка.\",\"Значок для действия закрытия в мини-приложениях.\",\"Значок для перехода к предыдущему расположению в редакторе.\",\"Значок для перехода к следующему расположению в редакторе.\",\"Следующие файлы были закрыты и изменены на диске: {0}.\",\"Следующие файлы были изменены несовместимым образом: {0}.\",\"Не удалось отменить \\\"{0}\\\" для всех файлов. {1}\",\"Не удалось отменить \\\"{0}\\\" для всех файлов. {1}\",\"Не удалось отменить операцию \\\"{0}\\\" для всех файлов, так как были внесены изменения в {1}\",\"Не удалось отменить действие \\\"{0}\\\" для всех файлов, так как в {1} уже выполняется операция отмены или повтора действия\",\"Не удалось отменить действие \\\"{0}\\\" для всех файлов, так как уже выполнялась операция отмены или повтора действия\",\"Вы хотите отменить \\\"{0}\\\" для всех файлов?\",\"&&Отменить действие в файлах {0}\",\"Отменить этот &&файл\",\"Не удалось отменить действие \\\"{0}\\\", так как уже выполняется операция отмены или повтора действия\",\"Вы хотите отменить \\\"{0}\\\"?\",\"&&Да\",\"Нет\",\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов. {1}\",\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов. {1}\",\"Не удалось повторить операцию \\\"{0}\\\" для всех файлов, так как были внесены изменения в {1}\",\"Не удалось повторить действие \\\"{0}\\\" для всех файлов, так как для {1} уже выполняется операция отмены или повтора действия.\",\"Не удалось повторить действие \\\"{0}\\\" для всех файлов, так как уже выполнялась операция отмены или повтора действия\",\"Не удалось повторить действие \\\"{0}\\\", так как уже выполняется операция отмены или повтора действия\",\"Рабочая область кода\"];\nglobalThis._VSCODE_NLS_LANGUAGE=\"ru\";"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,WAAW,qBAAqB,CAAC,YAAY,kFAAiB,+FAAoB,4EAAgB,+LAAoC,kFAAiB,oGAAoB,2TAAiE,iyCAA0P,4CAAc,4FAAsB,oEAAkB,wFAAuB,uEAAqB,yIAA2B,yDAAY,gEAAc,2IAA6B,uCAAS,gHAAsB,0IAA4B,mFAAkB,mFAAkB,6CAAU,wFAAkB,wHAAyB,KAAK,mCAAU,WAAW,2JAAmC,2UAAkE,2UAAkE,iFAA0B,2UAAkE,OAAO,QAAQ,MAAM,UAAU,OAAO,QAAQ,MAAM,QAAQ,OAAO,QAAQ,mDAAW,6CAAU,OAAO,QAAQ,MAAM,UAAU,OAAO,QAAQ,MAAM,QAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,0PAAkD,0PAAkD,8KAAkC,qDAAa,mDAAW,2DAAc,6CAAU,wEAAiB,gEAAc,6QAAsD,+EAAmB,qhCAAoQ,2RAAqD,8QAAkD,4OAA8C,6cAAwF,4HAAwB,gHAAsB,wKAAiC,0LAAoC,0LAAoC,0GAAqB,yLAAmC,kKAAgC,wKAAiC,mhBAAuG,6gBAAsG,6gBAAsG,6CAAU,snBAAwH,iHAAuB,0FAAoB,uFAAsB,oRAA4E,uCAAS,wHAA8B,sMAAgD,8GAA8B,kGAA4B,0VAAuE,2JAA8B,2JAA8B,iKAA+B,iKAA+B,iKAAoC,uKAAqC,6HAAyB,4XAAsE,oLAAmC,gHAAsB,0GAAqB,+dAAyF,uKAAgC,wFAAuB,kRAAsD,iKAA+B,kRAAsD,wFAAuB,qLAAoC,0MAA+C,gNAAgD,gIAAiC,sIAAkC,iKAA+B,0GAAqB,sVAAmE,uYAA4E,0SAA0D,0SAA0D,gTAA2D,mNAAyC,sRAAqD,gRAAoD,uHAAwB,mDAAW,gqBAAoI,u4BAAqO,4oBAAoI,+fAA0G,sRAAqD,ulBAAkH,wMAAwC,+PAAkD,8TAA+D,mPAAgD,4jBAAgH,0SAA0D,gTAA2D,6cAA4G,gYAA0E,+eAAoG,kjBAA2G,+WAAwE,saAAkF,0vBAAmJ,wxBAAkM,2VAAmE,uUAA8D,uUAA8D,oiBAA4G,uUAA8D,uUAA8D,uwBAAsJ,icAAyF,qZAAgF,4gBAAmG,qfAAgG,ikBAAgH,+qBAAoI,+hBAAuG,khBAAyG,8NAAoD,wMAAwC,kRAAsD,wSAA6D,qOAA4C,2OAA6C,yXAAwE,iWAAoE,mhBAAqG,ugBAAmG,+cAAwF,upBAAgI,mmBAAoH,8eAAmG,+UAAiE,4SAA4D,4kBAAiH,oSAAyD,8sBAA0I,wYAA6E,wYAA6E,yeAA8F,8nBAA2H,sdAA0F,8hBAA2G,iZAAgF,wTAA6D,sfAAgG,6YAA4E,8fAAyG,iiCAA+M,woBAAgI,yvBAA2L,opBAAwJ,m1BAA4L,gtBAA8L,+uBAAoM,2hCAA2P,kMAAuC,mWAA8F,sjBAAuI,iRAAqD,4TAA6D,4YAA4E,ohBAA2L,wgBAAsG,iiBAA2G,kgBAAqG,wgBAAsG,gfAAkG,qwBAAsJ,m0BAAkK,+vBAAqJ,0rBAAuI,oyBAA4J,2NAA4C,qYAA0E,mdAA4F,4eAAkH,gVAAkE,ymCAA2N,25BAAkL,+hCAAwM,6JAAgC,iXAA0E,ueAAiG,qSAA0D,wbAAqF,0VAAkE,stCAA8O,mZAAwF,gOAA4C,kKAAgC,waAAoF,wXAA4E,wKAAiC,o3BAA8K,4iBAA+G,8RAAwD;AAAA;AAAA;AAAA,ybAAqS,+MAA0C,0PAAkD,oaAAqF,8mBAA0H,wjBAAiH,kKAAgC,kTAA6D,kRAAsD,iSAAgE,+UAAiE,0kBAA+G,wdAA4F,qbAA8F,6TAA8D,6xBAAwJ,wWAAsE,oXAAwE,olBAAoH,idAA0F,qWAAmE,uUAA8D,6KAAiC,gOAA4C,8PAAiD,gTAA2D,o4DAAiX,6JAAgC,6MAAwC,ocAAuF,uNAA6C,6MAAwC,gdAAyF,6JAAgC,sqCAAiO,6WAAsE,2QAAoD,iRAAqD,4RAAsD,yXAAwE,uRAAsD,6RAAuD,wSAAwD,6MAAwC,yNAA0C,uYAA4E,kmBAAmH,+uBAAsK,4hBAAyG,izBAAkK,4XAA2E,8VAAsE,oSAAyD,qXAAyE,mcAAsF,qjBAAyG,whBAAqG,8UAAgE,2YAA2E,0lCAAiN,kSAAuD,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,qiBAA6G,wYAA6E,8MAAyC,qXAAyE,oNAA0C,gQAAmD,ggBAAiG,+XAAyE,ySAAyD,qVAAkE,uaAAmF,8WAAuE,yNAA0C,2aAAkF,sOAA6C,gaAAiF,yUAAgE,mUAA+D,4yBAA6J,udAA2F,scAAyF,stBAAmK,wUAAyE,2VAA6E,mZAAwF,wTAAwE,i3CAAsR,oXAAwE,yRAAwD,4YAA4E,gYAA0E,qlBAAqH,6gBAAoG,0eAAiJ,wSAA8E,0SAAgF,6SAAmF,4SAAkF,ypCAAuS,uSAA6E,0SAAgF,uSAA6E,wSAA8E,2SAAiF,wSAA8E,0SAAgF,uSAA6E,0SAAgF,sSAA4E,uSAA6E,0SAAgF,sSAA4E,4SAAkF,ySAA+E,sSAA4E,uSAA6E,sSAA4E,2SAAiF,6SAAmF,wSAA8E,+SAAqF,ySAA+E,qXAAqF,6VAAiF,wTAA8D,ySAAwF,+8BAA4N,+8BAA4N,mQAAsD,0WAAwE,6fAAmG,oeAA8F,0PAAkD,oqBAA0I,muBAAgJ,2XAA0E,2mBAAuH,qVAAkE,iwBAAqJ,2aAAkF,inBAAwH,u6BAAqM,geAA+F,u5BAAmL,wlDAA0T,sHAAuB,qaAAiF,wXAAuE,+dAA8F,2nBAAwH,4ZAA6E,6fAAmG,wpBAA4H,smBAAuH,uiBAA0G,goBAA6H,scAAyF,8XAAwE,qeAA+F,ilBAAiH,iRAAqD,+PAAkD,iiBAAyG,kvBAA0J,sgCAA4N,kxBAAuJ,sZAA4E,iNAA4C,iNAA4C,mjBAA4G,6vBAAiJ,8NAAoD,8LAA6C,qfAA8H,+gBAAsG,6YAA6E,8UAAgE,8PAAiD,2TAA4D,ykBAA8G,6ZAAmF,qZAAgF,2LAAqC,iMAAsC,yeAA8F,gOAA4C,8TAA+D,mRAAuD,o6BAAmN,wUAA0F,+PAAwE,mTAAyF,2YAAsG,qiBAAwG,4MAA4C,oSAAyD,+NAA2C,2dAA0F,wVAAqE,yRAAwD,otBAA2I,mSAAwD,6UAA+D,mUAA+D,kZAA6E,qjCAA0M,idAA0F,kKAAgC,68BAAuL,siBAAyG,gkBAA+G,yPAAiD,yPAAiD,41BAA0K,obAAsF,oLAAmC,qZAA0F,0UAAwE,wbAA0F,0SAA0D,qOAA0E,mOAAwE,+kCAA8S,uOAA8C,wMAAwC,2aAAkF,qiBAAwG,wHAAyB,iOAA6C,ubAAoF,8TAA+D,gVAAkE,gOAA4C,kPAA+C,qeAA+F,4kBAAiH,saAAkF,sYAA2E,+SAAgF,wWAAsE,yZAA+E,oKAAkC,qSAA0D,yiBAA4G,6TAA8D,yPAAiD,iMAAsC,qSAA0D,2XAA0E,onBAAsH,+ZAAgF,g4BAA2K,+UAAsE,0cAAwF,4RAAsD,ycAAuF,6gBAAoG,8ZAA+E,+NAA2C,+QAAmD,6UAA+D,6UAA+D,gWAAmE,6MAAwC,6bAAqF,sbAAmF,mzBAA0J,ydAAkG,ulBAA4H,8eAA8F,iMAAsC,w0BAAkM,+lBAAkJ,icAAoF,4fAAuG,wIAA+B,yjBAAmI,kIAA8B,6UAA+D,gRAAoD,gWAAmE,yZAA+E,kbAAoF,qQAAmD,ovBAAuJ,0jBAAmH,6pBAAiI,wMAAwC,kRAAsD,uLAA4D,wcAAiH,8OAAgD,uWAAmH,0pBAA8H,seAAgG,iQAAoD,iQAAoD,05BAA2L,uJAA+B,k/BAA4M,sQAAoD,8HAA0B,4iBAA0G,iWAAoE,gxBAAqJ,qYAA0E,ozBAA2J,2IAA6B,6JAAgC,gOAA4C,uYAAkI,iRAAqD,mZAA8I,oOAAgD,oOAAgD,oOAAgD,oOAAgD,oOAAgD,oOAAgD,qRAAyD,qRAAyD,qRAAyD,qRAAyD,qRAAyD,qRAAyD,6MAAwC,8XAA2G,6MAAwC,gUAA0F,8HAA0B,yOAAqD,8HAA0B,gMAAqC,4NAA6C,wMAAwC,2YAAgF,4XAA2E,woCAAwQ,yRAAwD,oUAAgE,uQAAqD,gwBAAoJ,qPAAkD,qSAA0D,4VAAoE,wZAAmF,wZAAmF,wZAAmF,wZAAmF,wZAAmF,wZAAmF,4OAA8C,ieAAgG,ieAAgG,ieAAgG,ieAAgG,ieAAgG,ieAAgG,qdAA8F,qdAA8F,qdAA8F,qdAA8F,qdAA8F,qdAA8F,uTAA6D,qSAA0D,iSAA2D,wmBAAyH,mdAA4F,qQAAmD,uPAA+C,oTAA0D,KAAK,wUAA+D,iPAA8C,yPAAiD,0gBAAiG,ydAAwF,sJAA8B,qVAAkE,qVAAkE,oHAA0B,gIAA4B,oIAAiD,4LAAsC,oNAA0C,kSAA4D,gOAA4C,uOAA8C,gOAA4C,sMAAsC,2LAAqC,2OAA6C,wYAA6E,uKAAgC,iRAAqD,oPAAiD,sKAAyC,gOAA4C,0NAA2C,0NAA2C,+PAAkD,oNAA0C,iRAAqD,qQAAmD,kMAAuC,kPAA+C,kRAAsD,iRAAqD,+SAA0D,yXAAwE,kXAAsE,4bAAoF,uCAAS,gHAAsB,iCAAQ,yDAAY,qEAAc,2EAAe,sHAAuB,6CAAU,2BAAO,2BAAO,6CAAU,yDAAY,2BAAO,iCAAQ,uCAAS,oGAAoB,OAAO,iCAAQ,uCAAS,mDAAW,iCAAQ,mDAAW,uCAAS,yDAAY,4EAAgB,+DAAa,YAAY,4EAAgB,2BAAO,kKAAgC,uIAA8B,2OAA6C,kFAAiB,4JAA+B,kGAAuB,4KAAqC,sHAAuB,qMAAqC,4JAAyC,8FAAwB,kDAAe,+IAA4B,0KAAwC,4MAAuC,iMAAsC,oSAAyD,gMAAqC,wQAAsD,yFAAmB,kFAAiB,kFAAiB,2FAAqB,kTAA6D,gMAAqC,sMAAsC,kIAAyB,qDAAa,mDAAW,mDAAW,mDAAW,iEAAe,qEAAc,qEAAc,qEAAc,qDAAa,mDAAW,mDAAW,mDAAW,gMAAqC,kFAAiB,kFAAiB,+DAAa,+DAAa,2TAA4D,yKAAkC,6RAAuD,uRAAsD,+ZAAgF,gOAA4C,sYAA2E,mHAAyB,sMAAsC,iRAA4D,kKAAuC,uPAA+C,sMAAsC,wEAAiB,qRAA2D,oLAA0C,4PAA+C,sPAA8C,6IAA+B,8SAA8D,uMAA8C,wQAAiD,8NAA0C,sHAAuB,mQAAiD,4EAAgB,wPAAgD,gGAAqB,uKAAgC,uYAA4E,gpBAA8H,izBAA4K,+JAA4C,0GAAqB,sHAAuB,2IAA6B,gHAAsB,6CAAU,+DAAa,+DAAa,qEAAc,mIAA0B,0IAA4B,sbAAwF,4lBAAuH,8jBAAkH,mrBAAwI,mrBAAwI,8DAAiB,oYAA8E,mIAA+B,6HAAyB,0MAA+C,8FAAmB,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,kQAAgD,+KAAmC,wIAA0B,8IAA2B,sPAA8C,yKAAkC,0DAAa,0GAAqB,2GAAsB,6FAAkB,yDAAY,yDAAY,mDAAW,yIAA2B,uCAAS,sMAAsC,mIAA0B,mIAA0B;AAAA,48BAA+N,yEAAkB,qGAAqB,yNAA0C,wJAAgC,qLAA2C,8RAAwD,kbAAoF,+IAA4B,2JAA8B,6HAAyB,uDAAe,uDAAe,4EAAgB,4EAAgB,2JAA8B,2JAA8B,wDAAgB,KAAK,mNAAyC,kJAA+B,uRAAsD;AAAA,KAA+C;AAAA,KAAkD,qbAAyF,0UAAiE,iCAAQ,mCAAU,2GAAsB,+FAAoB,gEAAc,gEAAc,oHAA0B,6QAAsD,4UAAwE,4GAA4B,4GAA4B,+IAA4B,qJAA6B,mDAAW,qDAAa,0WAAwE,sXAA0E,+XAAgF,mVAAuE,sWAA2E,gWAA0E,gWAA0E,6EAAiB,iCAAQ,iCAAQ,4HAAwB,sHAAuB,+FAAoB,6CAAU,mDAAW,mDAAW,mDAAW,sEAAe,gHAAsB,weAAkG,uBAAa,kIAAyB,mEAAiB,oEAAyB,gFAAgC,oEAAyB,y7BAAgN,+DAAa,4HAAwB,mDAAW,wIAA0B,gHAAsB,2MAAsC,8KAAkC,iHAAuB,6HAAyB,4JAA+B,wKAAiC,sEAAe,kFAAiB,sMAAsC,0QAAmD,oQAAkD,wSAAwD,kNAAwC,gFAAoB,ysBAA0I,2VAAmE,6WAAsE,6TAA8D,iTAA4D,4VAAoE,wWAAsE,4LAAsC,sLAAqC,0LAAoC,0LAAoC,oLAAmC,kIAAyB,+LAAoC,uVAAoE,2NAA4C,6VAAqE,iOAA6C,kdAA2F,4GAAuB,wdAA4F,kHAAwB,uCAAS,uFAAiB,+DAAa,mDAAW,mBAAc,yEAAuB,yEAAuB,8TAA+D,+WAAwE,8WAAuE,+ZAAgF,2aAAkF,4dAA2F,4QAAqD,iCAAQ,qEAAc,uJAAsC,8HAA0B,yHAA0B,+DAAa,iJAAqC,uHAAwB,mHAAyB,iJAAqC,uHAAwB,oGAAoB,gLAA2C,uJAA+B,kJAA+B,+DAAa,iJAAqC,wHAAyB,yHAA0B,wHAAgC,+FAAoB,iGAAsB,uCAAS,uCAAS,2EAAe,iHAA8B,uCAAS,uHAAwB,+IAA4B,sHAAuB,iHAAuB,kIAAyB,gJAA6B,+IAA4B,uHAAwB,kIAAyB,+FAAoB,wFAAkB,oIAA2B,0NAAgD,keAAqG,sDAAc,YAAY,4CAAc,2CAAa,uCAAS,2MAAsC,kIAAyB,uCAAS,mHAAmC,uHAAuC,wHAAmC,uIAAwC,uHAAwB,2GAA2B,+HAAgC,oKAAuC,udAA2F,iJAAwC,4DAAoB,2TAA4D,2TAA4D,yPAAiD,6WAAsE,yfAAoG,gdAAyF,4ZAA6E,qJAA6B,+IAA4B,qJAA6B,2JAA8B,wMAAwC,kMAAuC,4JAA+B,sJAA8B,4+BAAkM,ieAA2F,iTAA4D,2SAA2D,iTAA4D,uTAA6D,yPAAiD,mPAAgD,0UAAiE,oUAAgE,gOAA4C,wWAAsE,sDAAc,gqBAAwJ,slBAAiJ,uPAAoD,iPAA8C,uPAAoD,iPAA8C,sHAAuB,kIAAyB,oKAAkC,kIAAyB,mHAAyB,wKAAiC,6MAAwC,0LAAoC,kKAAgC,yIAA2B,qQAAmD,0LAAoC,oLAAmC,wNAAyC,gMAAqC,0LAAoC,oQAAkD,4OAA8C,kPAA+C,uMAAuC,2LAAqC,+SAA0D,iMAAsC,+PAAkD,4TAA6D,yKAAkC,6CAAe,8CAAgB,gDAAkB,6CAAe,weAAkG,6HAA8B,oGAAoB,sPAA8C,gPAA6C,2MAAsC,yQAAkD,4EAAgB,+QAAmD,kFAAiB,6KAAiC,6CAAU,uKAAgC,kNAAwC,wNAAyC,8PAAiD,siBAA8G,2TAA4D,maAAoF,sEAAe,ySAAyD,+SAA0D,YAAY,iCAAQ,iCAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,uKAAgC,iKAA+B,2JAA8B,yIAA2B,4IAA8B,mIAA0B,4IAA8B,4HAAwB,8HAA0B,yIAA2B,kJAA+B,mIAA0B,kJAA+B,8KAAkC,4JAA+B,iKAA+B,4MAAuC,kFAAiB,8FAAmB,8FAAmB,iHAAuB,iHAAuB,+FAAoB,qGAAqB,qGAAqB,wNAAyC,8KAAkC,wKAAiC,8KAAkC,iPAA8C,gLAAsC,4RAAwD,gJAA6B,qMAAqC,wUAA+D,sVAAwE,yTAA+D,oGAAoB,+FAAoB,0LAAyC,2LAA0C,6LAA4C,0LAAyC,8GAAyB,kFAAiB,+SAA0D,6FAAuB,yGAAyB,iHAAuB,mHAAyB,iHAAuB,mHAAyB,iMAAsC,6LAAuC,uHAAwB,uHAAwB,4RAAsD,mKAAiC,yVAAiE,yKAAkC,qWAAmE,kaAA8E,2OAA6C,yHAA0B,6HAAyB,oLAAmC,0LAAoC,0LAAoC,gMAAqC,gMAAqC,ySAAyD,+SAA0D,wDAAgB,yUAAgE,sQAAoD,6CAAU,+PAAkD,2LAAqC,mPAAgD,2NAA4C,2VAAmE,6YAA6E,uYAA4E,wWAAsE,qaAAiF,6JAAgC,sNAA4C,2SAA2D,uWAAqE,yPAAiD,2QAAoD,2SAA2D,8TAA+D,gKAAwC,8FAAwB,mWAAqF,uTAAuE,2cAAyF,kXAAsE,gMAAqC,6HAAyB,4EAAgB,4EAAgB,mDAAgB,yDAAiB,6CAAe,mDAAgB,iFAAqB,qEAAmB,6CAAe,+DAAkB,mDAAgB,+DAAkB,qEAAmB,0GAA0B,6CAAe,wFAAuB,6CAAe,yDAAiB,iFAAqB,kIAA8B,6CAAe,uCAAc,mDAAgB,uCAAc,sHAA4B,mDAAgB,uCAAc,iCAAa,+DAAkB,gVAAkE,mUAA+D,mIAA0B,obAAiF,0GAAmC,sGAA2B,gMAAkD,4RAAsD,4RAAsD,sHAAuB,sfAA4F,8SAAyD,uSAAuD,mVAAgE,mVAAgE,yOAAqD,qRAAyD,2ZAAiF,sMAAsC,uCAAS,uKAAgC,wHAAyB,uKAAgC,0FAAoB,2QAAoD,sYAA2E,4YAA4E,uLAAsC,qEAAc,qEAAc,6CAAU,iCAAQ,6CAAU,6CAAU,6CAAU,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,eAAK,uCAAS,6CAAU,2BAAO,uCAAS,qBAAM,2BAAO,2BAAO,uCAAS,mDAAW,6CAAU,uCAAS,6CAAU,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,qBAAM,0NAA2C,gHAAsB,kHAAwB,8JAAiC,0OAA4C,yrBAAoI,mNAAyC,8SAAyD,6PAAgD,sVAAmE,oGAAoB,6OAA+C,yNAA0C,8PAAiD,0ZAAgF,uRAAsD,sWAA6E,sUAAsE,yYAAyE,4RAAkE,wIAA0B,mDAAW,mDAAW,mDAAW,mDAAW,mDAAW,wFAAkB,wFAAkB,qOAA4C,uJAA+B,mKAAiC,2QAAoD,8WAAuE,mZAA8E,mUAA+D,4TAA6D,0eAA+F,2VAAmE,sDAAc,yIAA2B,+DAAa,eAAe,UAAU,WAAW,mEAAsB,6CAAU,sDAAc,wZAA8E,yDAAY,grBAAqI,ksBAAwI,0qBAAoI,oqBAAmI,4rBAAuI,8sBAA0I,otBAA2I,mvBAAiJ,grBAAqI,8pBAAkI,oqBAAmI,oqBAAmI,grBAAqI,ksBAAwI,oqBAAmI,2tBAA6I,0qBAAoI,0qBAAoI,uuBAA+I,0oBAAkI,oqBAAmI,grBAAqI,4rBAAuI,0qBAAoI,srBAAsI,0qBAAoI,qtBAA4I,0qBAAoI,4rBAAuI,0qBAAoI,2tBAA6I,0qBAAoI,ksBAAwI,ycAA4F,gUAAiE,uNAA6C,8vCAAwP,oTAA0D,+UAAiE,6UAAoE,uUAA8D,+SAA0D,wNAAyC,iZAA2F,2YAAqF,uFAAsB,4MAAiD,sHAAuB,gMAAqC,iPAA8C,kKAAgC,mNAAyC,+LAAoC,gPAA6C,uKAAgC,wNAAyC,qLAAyC,sPAAmD,uKAAgC,+NAAgD,iIAA6B,6TAAqE,sMAAsC,mQAAiD;AAAA;AAAA,ghBAAwR,mPAAgD,+DAAa,80BAAsK,g4BAAgL,+sBAA2I,kaAAmF,2YAAgF,sOAA6C,yxBAAyJ,i1BAAoK,6zBAA+J,yNAA0C,+NAA2C,qOAA4C,4EAAgB,yFAAmB,uCAAS,yIAA2B,uFAAiB,mFAAkB,mFAAkB,mIAA0B,mIAA0B,gJAA6B,mDAAW,8HAA0B,kFAAiB,oLAAmC,uKAAgC,gHAAsB,uMAAuC,kFAAiB,+NAA2C,6KAAiC,8FAAmB,8FAAmB,gEAAc,gEAAc,6HAAyB,sEAAe,2JAA8B,oGAAoB,4HAAwB,8FAAmB,oLAAmC,oLAAmC,sJAA8B,sJAA8B,2JAA8B,+IAA4B,yIAA2B,8HAA0B,8HAA0B,qGAAqB,kFAAiB,kFAAiB,mDAAW,6CAAU,yDAAY,yDAAY,uCAAS,uCAAS,iHAAuB,+IAA4B,iFAAgB,6CAAU,2BAAO,2BAAO,yDAAY,qEAAc,YAAY,YAAY;AAAA,WAAmB,6BAAc,YAAY,uCAAS,4EAAgB,6CAAiB,qJAA6B,6QAA2D,6FAAuB,oHAA+B,yIAA2B,kWAAqE,oQAAkD,8KAAkC,2JAA8B,qJAA6B,qJAA6B,4PAA+C,yQAAkD,8QAAuD,kUAA8D,yUAAgE,kUAA8D,yUAAgE,iPAA8C,kyBAA6L,gWAA0E,4aAA+F,2QAAoD,oLAAmC,qnBAAoJ,6CAAwB,yJAAmC,4HAAwB,uQAA0D,2JAA8B,8OAAgD;AAAA,0DAAuC,kNAA6C,kNAA6C,oNAA+C,oLAAmC,kZAAkF,gNAA2C,qOAA4C,8EAAuB,iOAA6C,mFAAuB,0GAA+B,+GAAoC,mMAAwC,opBAAgJ,2JAA8B,sVAAwE,0WAA6E,+NAAqD,+NAAqD,4EAAgB,qOAA0E,mOAAwE,+0DAAgX,qlCAA2N,+3BAA0K,mUAA+D,qNAA2C,0UAAiE,4XAA2E,qZAA0F,wVAAqE,4pBAAgI,yKAAkC,qVAA4E,8yBAA0J,0/BAAiM,4pBAAgI,upBAA0I,sNAAuG,8PAAiD,sRAAqD,8ZAAyF,g/BAAsM,qSAA0D,2dAA+F,w0BAAuM,uCAAS,uFAAiB,+DAAa,kIAAyB,wFAAkB,0GAAqB,kFAAiB,wFAAkB,WAAW,0IAAmC,WAAW,qYAA0E,uOAA8C,sSAA2D,iCAAQ,mYAAkF,YAAY,sQAAoD,2gCAAwM,i9BAAgM,69BAAkM,6HAAyB,oEAAkB,iDAAc,eAAK,uCAAS,uCAAc,iCAAQ,sEAAe,2LAA4C,kfAAkG,osBAA0I,ynBAA2H,kVAAoE,qPAAkD,snBAA6H,mjBAA4G,omBAAqH,iuBAAmJ,sNAA4C,maAAoF,6JAAgC,iMAAsC,sWAAoE,iNAA4C,6NAA8C,sNAA4C,4LAAsC,0NAA2C,wRAAuD,4QAAqD,kRAAsD,oSAAyD,wRAAuD,wRAAuD,4GAAuB,6OAA+C,8MAAyC,4VAAoE,8MAAyC,+MAA0C,gSAA0D,wXAA8E,y3BAA8K,qnCAA6N,sqBAAqI,+RAAyD,8TAA+D,stBAA6I,+UAAiE,8WAAuE,mxBAAwJ,sYAA2E,qaAAiF,iTAA4D,0UAAiE,kHAAwB,0IAA4B,wRAAuD,oqBAAmI,i1BAAoK,qTAAgE,+KAAmC,oNAA0C,4oBAA+H,+PAAkD,oqBAAmI,0NAA2C,oNAA0C,kuBAA+I,o2BAAwK,oPAAiD,iTAA4D,gQAAmD,yRAAwD,+NAA2C,kKAAgC,6RAAuD,gOAA4C,+SAA0D,kPAA+C,gSAA0D,8bAAsF,yOAAgD,4QAAqD,8RAAwD,mUAA+D,iWAAoE,4oBAA+H,goBAA6H,goBAA6H,onBAA2H,iNAA4C,qMAA0C,yXAAwE,6WAAsE,4LAAsC,gLAAoC,qQAAmD,wuBAA2I,0SAA0D,uWAAqE,oSAAyD,uUAAqE,yVAAwE,2VAAmE,6WAAsE,6WAAsE,0PAAkD,qLAAoC,0PAAkD,uMAAuC,2QAAoD,qyBAA2J,wxBAAwJ,0yBAA2J,8xBAAyJ,+xBAA0J,2yBAA4J,gYAA0E,yZAA+E,+ZAAgF,ifAAiG,iwBAAqJ,wxBAAwJ,gXAAyE,4aAAmF,saAAkF,8EAAkB,qIAA4B,sGAAsB,wRAAuD,4QAAqD,sQAAoD,yUAAgE,oPAAiD,qUAAmE,2ZAAmF,qUAAmE,yWAAyE,+bAAyF,yWAAyE,yTAAiE,+YAAiF,yTAAiE,sJAA8B,qLAAoC,6MAAwC,8KAAkC,uJAA+B,oIAA2B,0FAAoB,oKAAkC,4GAAuB,8MAAyC,iJAA8B,+RAAyD,wPAAgD,2LAAqC,6MAAwC,oQAAkD,uMAAuC,yNAA0C,iWAAoE,gLAAoC,qWAAwE,6OAA+C,kMAAuC,8XAA6E,itBAAwI,8wBAAmJ,muBAA2I,wwBAAkJ,2sBAAqK,svBAA6K,8sBAAqK,uvBAA8K,mpBAA0J,8rBAAkK,26BAAwL,+pBAA4J,ypBAA2J,u7BAA0L,wtBAAwK,2tBAAwK,sRAA+D,iUAAuE,iiBAAyG,0hBAAuG,qYAAoF,yfAAyG,8nBAA2H,iTAA4D,iWAAoE,kVAAoE,oWAAuE,ifAAiG,kVAAoE,sOAA6C,wPAAgD,0bAAuF,6OAA+C,0SAA0D,wMAAwC,mMAAwC,2IAA6B,wMAAwC,2ZAAiF,8VAAsE,0FAAoB,sLAAqC,yHAA0B,6QAAsD,0MAA0C,4NAA6C,wJAAgC,oPAAiD,+UAAiE,8OAAgD,4NAA6C,sOAA6C,sLAAqC,6GAAwB,+mBAAuI,8JAAiC,qUAAiE,yOAAgD,iJAA8B,qlBAAqH,imBAAuH,wdAA4F,qNAA2C,uTAA6D,gSAA0D,ocAAuF,stBAA6I,mxBAAwJ,6wBAAuJ,wRAAuD,8RAAwD,0LAA6D,4cAA0F,ifAAiG,+YAA+E,4QAAqD,sQAAoD,yUAAgE,yWAAuE,yKAAkC,qhBAAuG,0PAAkD,gQAAmD,6TAA8D,uTAA6D,sQAAyD,kSAA4D,uMAAmD,uMAAmD,gaAA6F,gkBAA2H,qjBAAqH,6LAA8C,sJAAmC,uGAAuB,oeAAqG,4GAA8B,iBAAO,qBAAM,8PAA6D,8PAA6D,saAA8F,mlBAA+H,2jBAAsH,0eAAsG,gHAAsB,EACpq3G,WAAW,qBAAqB", "names": [], "file": "nls.messages.ru.js"}