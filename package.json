{"name": "live-code-preview", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "server": "node server/index.js", "dev:full": "node dev-setup.js", "install:all": "npm install"}, "dependencies": {"@monaco-editor/react": "^4.4.1", "@octokit/rest": "^20.0.2", "axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.18.2", "express-session": "^1.17.3", "js-base64": "^3.7.5", "mongoose": "^7.6.0", "next": "^14.0.0", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}}