import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { Octokit } from '@octokit/rest';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const octokit = new Octokit({
    auth: session.accessToken,
  });

  try {
    switch (req.method) {
      case 'GET':
        const { data: repos } = await octokit.rest.repos.listForAuthenticatedUser({
          sort: 'updated',
          per_page: 100,
        });

        const formattedRepos = repos.map(repo => ({
          id: repo.id,
          name: repo.name,
          full_name: repo.full_name,
          description: repo.description,
          private: repo.private,
          html_url: repo.html_url,
          clone_url: repo.clone_url,
          updated_at: repo.updated_at,
          language: repo.language,
          stargazers_count: repo.stargazers_count,
          forks_count: repo.forks_count,
          default_branch: repo.default_branch,
        }));

        res.status(200).json(formattedRepos);
        break;

      case 'POST':
        const { name, description, private: isPrivate } = req.body;

        if (!name) {
          return res.status(400).json({ message: 'Repository name is required' });
        }

        const { data: newRepo } = await octokit.rest.repos.createForAuthenticatedUser({
          name,
          description: description || '',
          private: isPrivate || false,
          auto_init: true,
        });

        res.status(201).json({
          id: newRepo.id,
          name: newRepo.name,
          full_name: newRepo.full_name,
          description: newRepo.description,
          private: newRepo.private,
          html_url: newRepo.html_url,
          clone_url: newRepo.clone_url,
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('GitHub API error:', error);
    res.status(500).json({ 
      message: 'GitHub API error', 
      error: error.message 
    });
  }
}
