import { getProviders, signIn, getSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { FaGithub, FaCode, FaRocket } from 'react-icons/fa';

export default function SignIn({ providers }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession();
      if (session) {
        router.push('/');
      }
    };
    checkSession();
  }, [router]);

  const handleSignIn = async (providerId) => {
    setLoading(true);
    try {
      await signIn(providerId, { callbackUrl: '/' });
    } catch (error) {
      console.error('Sign in error:', error);
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Sign In - Live Code Preview</title>
        <meta name="description" content="Sign in to access GitHub integration features" />
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-xl p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-600 p-3 rounded-full">
                <FaCode className="text-white text-2xl" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Live Code Preview</h1>
            <p className="text-gray-600">Connect your GitHub account to unlock powerful features</p>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex items-center text-sm text-gray-600">
              <FaGithub className="mr-2 text-gray-400" />
              <span>Access your repositories</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <FaCode className="mr-2 text-gray-400" />
              <span>Load and edit files directly</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <FaRocket className="mr-2 text-gray-400" />
              <span>Save changes back to GitHub</span>
            </div>
          </div>

          {Object.values(providers).map((provider) => (
            <div key={provider.name}>
              <button
                onClick={() => handleSignIn(provider.id)}
                disabled={loading}
                className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <>
                    <FaGithub className="mr-2 text-xl" />
                    Sign in with {provider.name}
                  </>
                )}
              </button>
            </div>
          ))}

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              By signing in, you agree to our terms of service and privacy policy.
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps() {
  const providers = await getProviders();
  return {
    props: { providers },
  };
}
